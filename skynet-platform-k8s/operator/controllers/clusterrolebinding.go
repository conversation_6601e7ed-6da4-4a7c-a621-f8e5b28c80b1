package controllers

import (
	"context"
	"fmt"

	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"

	skynetv1alpha1 "iflytek.com/skynet/operator/api/v1alpha1"
)

// 创建 or 更新 clusterRoleBinding
func (r *SkynetAppReconciler) updateClusterRoleBinding(skynetApp *skynetv1alpha1.SkynetApp, clusterRoleBindingToUpdate *rbacv1.ClusterRoleBinding) error {

	clusterRoleBindingName := types.NamespacedName{
		Namespace: clusterRoleBindingToUpdate.Namespace,
		Name:      clusterRoleBindingToUpdate.Name,
	}

	// 查询 clusterRoleBinding 是否存在
	fmt.Printf("Get clusterRoleBinding [%s] for SkynetApp [%s] ...\n", clusterRoleBindingToUpdate.Name, skynetApp.Name)
	clusterRoleBinding := &rbacv1.ClusterRoleBinding{}
	if err := r.Get(context.TODO(), clusterRoleBindingName, clusterRoleBinding); err != nil {
		if errors.IsNotFound(err) {
			// clusterRoleBinding 不存在，则创建一个新的
			fmt.Printf("Get clusterRoleBinding [%s] for SkynetApp [%s] not found\n", clusterRoleBindingToUpdate.Name, skynetApp.Name)
			fmt.Printf("Create clusterRoleBinding [%s] for SkynetApp [%s] ...\n", clusterRoleBindingToUpdate.Name, skynetApp.Name)
			ctrl.SetControllerReference(skynetApp, clusterRoleBindingToUpdate, r.Scheme)
			if err := r.Create(context.TODO(), clusterRoleBindingToUpdate); err != nil {
				fmt.Printf("Create clusterRoleBinding [%s] for SkynetApp [%s] error\n", clusterRoleBindingToUpdate.Name, skynetApp.Name)
				return err
			}
			return nil
		} else {
			// 获取 clusterRoleBinding 出错
			fmt.Printf("Get clusterRoleBinding [%s] for SkynetApp [%s] error\n", clusterRoleBindingToUpdate.Name, skynetApp.Name)
			return err
		}
	} else {
		// clusterRoleBinding 已存在，则更新
		fmt.Printf("Get clusterRoleBinding [%s] for SkynetApp [%s] success\n", clusterRoleBindingToUpdate.Name, skynetApp.Name)
		clusterRoleBinding.Subjects = clusterRoleBindingToUpdate.Subjects
		clusterRoleBinding.RoleRef = clusterRoleBindingToUpdate.RoleRef
		ctrl.SetControllerReference(skynetApp, clusterRoleBinding, r.Scheme)
		fmt.Printf("Update clusterRoleBinding [%s] for SkynetApp [%s] ...\n", clusterRoleBindingToUpdate.Name, skynetApp.Name)
		if err := r.Update(context.TODO(), clusterRoleBinding); err != nil {
			fmt.Printf("Update clusterRoleBinding [%s] for SkynetApp [%s] error\n", clusterRoleBindingToUpdate.Name, skynetApp.Name)
			return err
		}
	}
	return nil
}
