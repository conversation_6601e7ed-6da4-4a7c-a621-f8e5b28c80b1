apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: skyline-demo-skyline
  name: skyline-demo-skyline
spec:
  replicas: 1
  selector:
    matchLabels:
      app: skyline-demo-skyline
  strategy: {}
  template:
    metadata:
      labels:
        app: skyline-demo-skyline
    spec:
      initContainers:
      - image: sealos.hub:5000/skynet/init:3.4.2-1011
        name: skynet-init
        command: ['/init', '--action-code', 'skyline-demo', '--plugin-code', 'skyline', '--index', '0', '--port', '30080']
        volumeMounts:
          - name: skynet-init-config
            mountPath: /init.properties
            subPath: init.properties
          - name: skyline-demo-skyline-resources
            mountPath: ./resources
      containers:
      - image: sealos.hub:5000/skynet/openjdk:8
        name: skyline-demo-skyline
        command: ['/bin/bash', '-c', '/iflytek/server/skynet/target.sh']
        #command: ['tail', '-f', '/dev/null']
        volumeMounts:
          - name: skyline-demo-skyline-resources
            mountPath: /iflytek/server/skynet
            subPath: ./iflytek/server/skynet
      volumes:
        - name: skynet-init-config
          configMap:
            name: skynet-init-config
        - name: skyline-demo-skyline-resources
          emptyDir: {}
