#/bin/bash

#if [[ ( -z $1 ) || ( $1 != 'tcp' && $1 != 'udp' ) ]];then
#  echo "argument 'tcp' or 'udp' is not provided!"
#  exit 1
#fi

OLD_IFS=$IFS
IFS=$'\n'

i=0
for line in `netstat -nap |grep -E 'tcp|udp'`
do
  proto3letter=${line:0:3}
  if [[ $proto3letter = 'tcp' ]];then
    listen=`echo $line |grep ' LISTEN '`
    if [[ ! -z $listen ]];then 
      netstat_array[$i]=$line
      let i++
    fi
  elif [[ $proto3letter = 'udp' ]];then
    netstat_array[$i]=$line
    let i++
  fi
done

#echo "PROTOCOL    PORT    PID    CMD"
printf "%-10s %-10s %-10s %s\n"    PROTOCOL    PORT    PID    CMD
for line in ${netstat_array[@]}
do
  protocol=`echo "$line" | awk '{print $1}'`
  col4=`echo "$line" | awk '{print $4}'`
  col6=`echo "$line" | awk '{print $6}'`
  col7=`echo "$line" | awk '{print $7}'`
  port=${col4##*:}
  #netstat命令输出行的列数，tcp和udp是不同的
  if [[ ${protocol:0:3} = 'tcp' ]];then
    pid=${col7%/*}
  else
    pid=${col6%/*}
  fi
  if [[ -e /proc/$pid/cmdline ]];then
    cmd=`cat /proc/$pid/cmdline`
  fi
  if [[ -z $cmd ]];then
    cmd='[unknown]'
  fi
  #echo "$protocol    $port    $pid    $cmd"
  printf "%-10s %-10s %-10s %s\n"  $protocol    $port    $pid    $cmd
done
  
IFS=$OLD_IFS
