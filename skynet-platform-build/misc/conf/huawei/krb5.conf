[kdcdefaults]
kdc_ports = 172.31.160.142:21732
kdc_tcp_ports = ""

[libdefaults]
default_realm = HADOOP.COM
kdc_timeout = 2500
clockskew = 300
use_dns_lookup = 0
udp_preference_limit = 1465
max_retries = 5
dns_lookup_kdc = false
dns_lookup_realm = false
renewable = false
forwardable = false
renew_lifetime = 0m
max_renewable_life = 30m
allow_extend_version = false
default_ccache_name = FILE:/tmp//krb5cc_%{uid}

[realms]
HADOOP.COM = {
kdc = 172.31.160.141:21732
kdc = 172.31.160.142:21732
admin_server = 172.31.160.141:21730
admin_server = 172.31.160.142:21730
kpasswd_server = 172.31.160.141:21731
kpasswd_server = 172.31.160.142:21731
kpasswd_port = 21731
kadmind_port = 21730
kadmind_listen = 172.31.160.142:21730
kpasswd_listen = 172.31.160.142:21731
renewable = false
forwardable = false
renew_lifetime = 0m
max_renewable_life = 30m
acl_file = /opt/huawei/Bigdata/FusionInsight_BASE_8.1.2.2/install/FusionInsight-kerberos-1.18/kerberos/var/krb5kdc/kadm5.acl
dict_file = /opt/huawei/Bigdata/common/runtime/security/weakPasswdDic/weakPasswdForKdc.ini
key_stash_file = /opt/huawei/Bigdata/FusionInsight_BASE_8.1.2.2/install/FusionInsight-kerberos-1.18/kerberos/var/krb5kdc/.k5.HADOOP.COM
}

[domain_realm]
.hadoop.com = HADOOP.COM

[logging]
kdc = SYSLOG:INFO:DAEMON
admin_server = SYSLOG:INFO:DAEMON
default = SYSLOG:NOTICE:DAEMON
