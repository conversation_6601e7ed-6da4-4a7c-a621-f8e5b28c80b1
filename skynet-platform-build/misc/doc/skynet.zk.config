#App Config Dashboard (ACD) dump created on :Sat Dec 29 11:50:33 CST 2018
/skynet/cluster=_desc=分布式集群配置
/skynet/cluster=_online=在线节点监控[动态]
/skynet/cluster=_topology=分布式计算集群拓扑节点配置[静态]
/skynet/cluster=cluster.name=Skynet分布式微服务托管平台
/skynet/cluster/topology=_desc=集群拓扑配置
/skynet/plugin/ant/action=_desc=Action服务定义列表 命名规则： {action}-v{版本号}[-{其他标识}] 如： ist-v10-chin
/skynet/plugin/ant/setting/_logger=_desc=key:命名空间，value:日志级别[OFF,ERROR,WARN,INFO,DEBUG,TRACE]
/skynet/plugin/ant/setting/_properties=_desc=当前插件properties配置
/skynet/plugin/ant/setting=_desc=skynet基础服务插件_自定义配置
/skynet/plugin/ant=_desc=Skynet基础服务插件
/skynet/plugin/ant=_name=基础平台-基础服务
/skynet/plugin/ant=_index=-100000
/skynet/plugin/ant=_version=3.4.15_Build${buildSid}
/skynet/plugin=_desc=Skynet插件配置中心
/skynet/setting/_properties=_desc=全局properties配置
/skynet/setting=_xlogger=skynet=ERROR\nskynet.boot=INFO
/skynet/setting=_desc=系统键值配置
/skynet=_desc=skynet配置管理中心
/skynet=_name=SKYNET服务托管-控制台
/skynet=cluster.desc=Skynet分布式计算集群配置中心
/skynet=plugin.desc=Skynet插件配置中心
/skynet=setting.desc=skynet全局自定义配置中心
/skynet=version=3.4.15_Build${buildSid}
/skynet/xmanager/action=_desc=服务定义
/skynet/xmanager/action=label=[{"title":"调式模式","code":"debug","extPlaceHolder":"","extTitle":"","boots":[]},{"title":"显示[Actuator]","code":"enableActuator","extPlaceHolder":"例如 [base64(user:pwd)@]/actuator","extTitle":"Actuator访问配置","boots":[]},{"title":"启用监控采集[Prometheus]","code":"enablePrometheusTarget","extPlaceHolder":"例如 [base64(user:pwd)@]/metrics:15s","extTitle":"Prometheus采集点配置","boots":[]},{"title":"启用Ant追踪[Skywalking]","code":"enableSkywalking","extPlaceHolder":"","extTitle":"","boots":["SpringBoot","SkynetBoot"]},{"title":"启用Nginx网关","code":"enableNginxGateway","extPlaceHolder":"请输入服务别名","extTitle":"服务别名","boots":[]},{"title":"参与流程计算(分布式计算平台)","code":"enableStream","extPlaceHolder":"","extTitle":"","boots":[]},{"title":"启用服务命名空间","code":"SERVICE_DISCOVERY_NAMESPACE","extPlaceHolder":"如 turing-ai，多个用分号分隔","extTitle":"命名空间","boots":[]}]
/skynet/xmanager/menu=_desc=菜单节点
/skynet/xmanager/menu=_nodes={"index":1,"mid":"manage","name":"控制台","icon":"globe","show":true,"menus":[{"index":1,"mid":"cluster","name":"服务管理","icon":"cubes","show":true,"url":"./ui_cluster"},{"index":2,"mid":"monitor","name":"集群状态","icon":"bar-chart","show":true,"url":"./ui_cluster-mon"},{"index":5,"mid":"deposit","name":"服务定义","icon":"sun","show":true,"url":"./ui_hosting"},{"index":6,"mid":"tool","name":"辅助工具","icon":"node","show":true,"url":"./ui_tool"}]}
/skynet/xmanager=_desc=Skynet管理中心的配置
/skynet/xmanager=diagnosis=[{"title":"Agent日志","items":[{"title":"Agent日志","desc":"Agent日志文件","cmd":"tail -120 ${SKYNET_HOME}/log/<EMAIL>"},{"title":"Skynet目录","desc":"Skynet工作目录","cmd":"ls ${SKYNET_HOME} -lh"},{"title":"Skynet版本信息","desc":"skynet-agent和skynet-manger版本信息","cmd":"cat ${SKYNET_HOME}/version.txt"}]},{"title":"IO统计","items":[{"title":"CPU磁盘统计","desc":"iostat统计分析，磁盘平均占用率超过95%显示告警","cmd":"sh ${SKYNET_HOME}/bin/utils/iostat.sh"}]},{"title":"进程内存TOP N","items":[{"title":"进程内存TOP N","desc":"基于top命令的采样统计分析，分析出采样时间(5秒)内，内存平均占用最高的前20个进程","cmd":"python ${SKYNET_HOME}/bin/utils/top.py mem 20 5"}]},{"title":"进程CPU TOP N","items":[{"title":"进程CPU TOP N","desc":"基于top命令的采样统计分析，分析出采样时间(5秒)内，CPU平均占用最高的前20个进程","cmd":"python ${SKYNET_HOME}/bin/utils/top.py cpu 20 5"}]},{"title":"硬件信息","items":[{"title":"CPU信息","desc":"CPU信息","cmd":"sh ${SKYNET_HOME}/bin/utils/cpu.sh"},{"title":"块设备信息","desc":"块设备信息","cmd":"lsblk"},{"title":"分区表","desc":"分区表","cmd":"fdisk -l"},{"title":"磁盘阵列信息","desc":"需要安装MegaCli，并将MegaCli64加入PATH环境变量","cmd":"MegaCli64 -AdpAllInfo -aALL"},{"title":"网卡信息","desc":"输出所有网卡的ethtool信息","cmd":"sh ${SKYNET_HOME}/bin/utils/ethtool.sh"}]},{"title":"系统信息","items":[{"title":"操作系统","desc":"OS版本","cmd":"cat /proc/version && echo ' ' && lsb_release -a"},{"title":"shell类型","desc":"查看sh命令指向的真实shell类型","cmd":"ls -al /bin/sh"},{"title":"环境变量","desc":"查看所有环境变量","cmd":"env"},{"title":"glibc版本","desc":"glibc版本","cmd":"ldd --version"},{"title":"资源限制","desc":"ulimit配置","cmd":"ulimit -a"},{"title":"selinux","desc":"selinux是否开启","cmd":"getenforce"}]},{"title":"服务器状态","items":[{"title":"内存状态","desc":"内存占用情况","cmd":"free -h"},{"title":"磁盘状态","desc":"磁盘空间占用情况","cmd":"sh ${SKYNET_HOME}/bin/utils/df.sh 85"},{"title":"服务器时间","desc":"服务器时间","cmd":"date"}]},{"title":"网络配置","items":[{"title":"IP配置","desc":"IP配置","cmd":"ifconfig"},{"title":"Host名称","desc":"hostname","cmd":"hostname"},{"title":"Host配置","desc":"host配置中需要配置hostname对应的IP地址","cmd":"cat /etc/hosts"},{"title":"DNS配置","desc":"DNS配置","cmd":"cat /etc/resolv.conf"}]},{"title":"TCP连接状态","items":[{"title":"连接状态","desc":"所有TCP连接状态","cmd":"netstat -nap | grep tcp"}]},{"title":"防火墙状态","items":[{"title":"防火墙状态","desc":"防火墙状态","cmd":"systemctl status firewalld.service"}]},{"title":"端口占用","items":[{"title":"端口占用","desc":"服务端口占用情况（tcp/udp)，四列数据分别为协议、端口号、PID、命令行","cmd":"sh ${SKYNET_HOME}/bin/utils/port.sh"},{"title":"端口状态统计","desc":"网络端口状态统计","cmd":"netstat -ant|awk '/^tcp/ {++state[$NF]} END {for(key in state) print (key,state[key])}'"}]},{"title":"Java环境","items":[{"title":"JDK版本","desc":"JDK版本","cmd":"java -version"},{"title":"Java进程","desc":"运行的Java进程","cmd":"jcmd"},{"title":"JDK部署目录","desc":"JDK部署目录","cmd":"which java"}]},{"title":"Python环境","items":[{"title":"Python版本","desc":"Python版本","cmd":"python --version"},{"title":"Python部署目录","desc":"Python部署目录","cmd":"which python |xargs ls -l"}]},{"title":"Docker环境","items":[{"title":"Docker版本","desc":"Docker版本","cmd":"docker -v"},{"title":"Docker容器(运行中)","desc":"Docker容器(运行中)","cmd":"docker ps"},{"title":"Docker容器(停止)","desc":"Docker容器(停止)","cmd":"docker ps -a | grep -E 'Exited|IMAGE'"},{"title":"Docker镜像","desc":"hostname","cmd":"docker images"},{"title":"Docker配置","desc":"Docker配置","cmd":"cat /etc/docker/daemon.json"}]},{"title":"GPU环境","items":[{"title":"CUDA版本","desc":"CUDA驱动版本","cmd":"cat /usr/local/cuda/version.txt"},{"title":"GPU信息","desc":"GPU信息","cmd":"nvidia-smi --format=csv --query-gpu=index,name,uuid,serial,utilization.gpu,utilization.memory,memory.total,memory.free,memory.used,temperature.gpu,compute_mode,clocks.current.graphics,clocks.current.memory"},{"title":"GPU状态","desc":"GPU运行负载状态","cmd":"nvidia-smi"}]},{"title":"AI引擎授权","items":[{"title":"Skylock授权服务状态","desc":"Skylock授权服务状态","cmd":"ps -ef|grep licServer|grep -v grep"},{"title":"Skylock授权能力列表","desc":"skylock授权能力列表","cmd":"sh ${SKYNET_HOME}/bin/utils/skylock.sh"},{"title":"Hasp授权服务状态","desc":"hasp授权服务状态","cmd":"systemctl status hasplmd.service"},{"title":"Hasp授权能力列表","desc":"hasp授权能力列表","cmd":"sh ${SKYNET_HOME}/bin/utils/aksubd.sh"},{"title":"Hasp_AuthCode授权服务状态","desc":"hasp_AuthCode授权服务状态","cmd":"systemctl status hasplmd.service"},{"title":"Hasp_Authcode授权能力列表","desc":"hasp_Authcode授权能力列表","cmd":"sh ${SKYNET_HOME}/bin/utils/aksubd-auth.sh"}]}]
/skynet/xmanager/users=_desc=xmanager用户列表\nkey：用户名 value：密码
/skynet/xmanager/users=admin=86c5b5d1093830f74c1ec2d54c5ea886774fe520b80681d0d734005fae265a097935550ac76a6c9d847b2560d41216e8