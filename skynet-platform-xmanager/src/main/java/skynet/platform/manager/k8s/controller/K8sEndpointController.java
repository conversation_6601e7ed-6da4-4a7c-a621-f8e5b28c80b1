package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sEndpoint;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sEndpointService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sEndpointController implements V3K8sEndpoint {

    private final K8sEndpointService k8sEndpointService;

    public K8sEndpointController(K8sEndpointService k8sEndpointService) {
        this.k8sEndpointService = k8sEndpointService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getEndpoints(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sEndpointService.getEndpoints(ip, k8sQuery);
        response.setData(results);
        log.debug("getEndpoints response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getEndpoint(String ip, String namespace, String endpointName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sEndpointService.getEndpoint(ip, namespace, endpointName);
        response.setData(result);
        log.debug("getEndpoint response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getEndpointYaml(String ip, String namespace, String endpointName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sEndpointService.getEndpointYaml(ip, namespace, endpointName);
        response.setData(result);
        log.debug("getEndpointYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Endpoint 管理", operation = "删除 Endpoint", message = "ip=#{#ip}, namespace=#{#namespace}, endpointName=#{#endpointName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> deleteEndpoint(String ip, String namespace, String endpointName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sEndpointService.deleteEndpoint(ip, namespace, endpointName);
        response.setData(result);
        log.debug("deleteEndpoint response:{}", response);
        return response;
    }

}
