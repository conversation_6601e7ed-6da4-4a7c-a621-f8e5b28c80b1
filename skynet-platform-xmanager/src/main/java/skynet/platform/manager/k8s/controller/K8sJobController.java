package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sJob;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sJobService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sJobController implements V3K8sJob {

    private final K8sJobService k8sJobService;

    public K8sJobController(K8sJobService k8sJobService) {
        this.k8sJobService = k8sJobService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getJobs(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sJobService.getJobs(ip, k8sQuery);
        response.setData(results);
        log.debug("getJobs response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getJob(String ip, String namespace, String jobName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sJobService.getJob(ip, namespace, jobName);
        response.setData(result);
        log.debug("getJob response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getJobYaml(String ip, String namespace, String jobName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sJobService.getJobYaml(ip, namespace, jobName);
        response.setData(result);
        log.debug("getJobYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Job 管理", operation = "删除 Job", message = "ip=#{#ip}, namespace=#{#namespace}, jobName=#{#jobName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> deleteJob(String ip, String namespace, String jobName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sJobService.deleteJob(ip, namespace, jobName);
        response.setData(result);
        log.debug("deleteJob response:{}", response);
        return response;
    }

}
