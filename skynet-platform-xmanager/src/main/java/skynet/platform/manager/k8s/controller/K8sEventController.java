package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sEvent;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sEventService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sEventController implements V3K8sEvent {

    private final K8sEventService k8sEventService;

    public K8sEventController(K8sEventService k8sEventService) {
        this.k8sEventService = k8sEventService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getEvents(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sEventService.getEvents(ip, k8sQuery);
        response.setData(results);
        log.debug("getEvents response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getEvent(String ip, String namespace, String eventName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sEventService.getEvent(ip, namespace, eventName);
        response.setData(result);
        log.debug("getEvent response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getEventYaml(String ip, String namespace, String eventName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sEventService.getEventYaml(ip, namespace, eventName);
        response.setData(result);
        log.debug("getEventYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Event 管理", operation = "删除 Event", message = "ip=#{#ip}, namespace=#{#namespace}, eventName=#{#eventName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> deleteEvent(String ip, String namespace, String eventName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sEventService.deleteEvent(ip, namespace, eventName);
        response.setData(result);
        log.debug("deleteEvent response:{}", response);
        return response;
    }

}
