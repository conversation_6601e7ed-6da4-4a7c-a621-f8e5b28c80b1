package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sUpdateCronJobHistoryLimit;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sCronJob;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sCronJobService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sCronJobController implements V3K8sCronJob {

    private final K8sCronJobService k8sCronJobService;

    public K8sCronJobController(K8sCronJobService k8sCronJobService) {
        this.k8sCronJobService = k8sCronJobService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getCronJobs(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sCronJobService.getCronJobs(ip, k8sQuery);
        response.setData(results);
        log.debug("getCronJobs response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getCronJob(String ip, String namespace, String cronJobName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCronJobService.getCronJob(ip, namespace, cronJobName);
        response.setData(result);
        log.debug("getCronJob response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getCronJobYaml(String ip, String namespace, String cronJobName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sCronJobService.getCronJobYaml(ip, namespace, cronJobName);
        response.setData(result);
        log.debug("getCronJobYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S CronJob 管理", operation = "删除 CronJob", message = "ip=#{#ip}, namespace=#{#namespace}, cronJobName=#{#cronJobName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> deleteCronJob(String ip, String namespace, String cronJobName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCronJobService.deleteCronJob(ip, namespace, cronJobName);
        response.setData(result);
        log.debug("deleteCronJob response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S CronJob 管理", operation = "暂停运行", message = "ip=#{#ip}, namespace=#{#namespace}, cronJobName=#{#cronJobName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> stopCronJob(String ip, String namespace, String cronJobName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCronJobService.stopCronJob(ip, namespace, cronJobName);
        response.setData(result);
        log.debug("stopCronJob response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S CronJob 管理", operation = "恢复运行", message = "ip=#{#ip}, namespace=#{#namespace}, cronJobName=#{#cronJobName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> resumeCronJob(String ip, String namespace, String cronJobName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCronJobService.resumeCronJob(ip, namespace, cronJobName);
        response.setData(result);
        log.debug("resumeCronJob response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S CronJob 管理", operation = "手工触发", message = "ip=#{#ip}, namespace=#{#namespace}, cronJobName=#{#cronJobName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> manualCronJob(String ip, String namespace, String cronJobName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCronJobService.manualCronJob(ip, namespace, cronJobName);
        response.setData(result);
        log.debug("manualCronJob response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S CronJob 管理", operation = "更新最大历史 Job 数", message = "ip=#{#ip}, namespace=#{#namespace}, cronJobName=#{#cronJobName}, k8sUpdateCronJobHistoryLimit=#{#k8sUpdateCronJobHistoryLimit}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> updateCronJobHistoryLimit(String ip, String namespace, String cronJobName, K8sUpdateCronJobHistoryLimit k8sUpdateCronJobHistoryLimit) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCronJobService.updateCronJobHistoryLimit(ip, namespace, cronJobName, k8sUpdateCronJobHistoryLimit);
        response.setData(result);
        log.debug("updateCronJobHistoryLimit response:{}", response);
        return response;
    }
}
