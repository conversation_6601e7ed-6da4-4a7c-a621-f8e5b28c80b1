package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sNamespace;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sNamespaceService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sNamespaceController implements V3K8sNamespace {

    private final K8sNamespaceService k8sNamespaceService;

    public K8sNamespaceController(K8sNamespaceService k8sNamespaceService) {
        this.k8sNamespaceService = k8sNamespaceService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getNamespaces(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sNamespaceService.getNamespaces(ip, k8sQuery);
        response.setData(results);
        log.debug("getNamespaces response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getNamespace(String ip, String namespaceName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNamespaceService.getNamespace(ip, namespaceName);
        response.setData(result);
        log.debug("getNamespace response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getNamespaceYaml(String ip, String namespaceName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sNamespaceService.getNamespaceYaml(ip, namespaceName);
        response.setData(result);
        log.debug("getNamespaceYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Namespace 管理", operation = "删除 Namespace", message = "ip=#{#ip}, namespaceName=#{#namespaceName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> deleteNamespace(String ip, String namespaceName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sNamespaceService.deleteNamespace(ip, namespaceName);
        response.setData(result);
        log.debug("deleteNamespace response:{}", response);
        return response;
    }

}
