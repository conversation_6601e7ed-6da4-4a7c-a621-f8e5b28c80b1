package skynet.platform.manager.k8s.service;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.ApiClient;
import skynet.platform.feign.model.*;

import java.util.List;

public interface K8sDeploymentService {

    /**
     * 获取 Deployment 列表
     */
    List<JSONObject> getDeployments(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 Deployment 详情
     */
    JSONObject getDeployment(String ip, String namespace, String deploymentName) throws Exception;

    /**
     * 获取 Deployment Yaml
     */
    String getDeploymentYaml(String ip, String namespace, String deploymentName) throws Exception;

    /**
     * 删除 Deployment
     */
    JSONObject deleteDeployment(String ip, String namespace, String deploymentName) throws Exception;

    /**
     * 重启 Deployment
     */
    JSONObject restartDeployment(String ip, String namespace, String deploymentName) throws Exception;

    /**
     * 伸缩
     */
    JSONObject updateDeploymentReplicas(String ip, String namespace, String deploymentName, K8sUpdateReplica k8sUpdateReplica) throws Exception;

    /**
     * 调整镜像版本
     */
    JSONObject updateDeploymentImages(String ip, String namespace, String deploymentName, K8sUpdateImage k8sUpdateImage) throws Exception;

    /**
     * 调整最大历史副本数
     */
    JSONObject updateDeploymentRevisionHistoryLimit(String ip, String namespace, String deploymentName, K8sUpdateRevisionHistoryLimit k8sUpdateRevisionHistoryLimit) throws Exception;

    /**
     * 回滚版本
     */
    JSONObject rollbackDeployment(String ip, String namespace, String deploymentName, K8sRollbackDeployment k8sRollbackDeployment) throws Exception;

    /**
     * 修改更新策略
     */
    JSONObject updateDeploymentStrategy(String ip, String namespace, String deploymentName, K8sUpdateDeploymentStrategy k8sUpdateDeploymentStrategy) throws Exception;

    /**
     * 在指定 Deployment 的所有容器上设置（或修改）环境变量
     *
     * @param namespace      命名空间   例如 "openebs"
     * @param deploymentName Deployment 名称 例如 "openebs-localpv-provisioner"
     * @param envKey         环境变量名 例如 "OPENEBS_IO_BASE_PATH"
     * @param envValue       环境变量值 例如 "/data/openebs/local"
     */
    void updateDeploymentEnvVar(String ip, String namespace, String deploymentName, String envKey, String envValue) throws Exception;

    /**
     *更新 openebs pv路径
     */
    void updateScBasePath(String ip, String scName, String newBasePath) throws Exception;
}
