package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.*;
import skynet.platform.feign.service.V3K8sDeployment;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sDeploymentService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sDeploymentController implements V3K8sDeployment {

    private final K8sDeploymentService k8sDeploymentService;

    public K8sDeploymentController(K8sDeploymentService k8sDeploymentService) {
        this.k8sDeploymentService = k8sDeploymentService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getDeployments(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sDeploymentService.getDeployments(ip, k8sQuery);
        response.setData(results);
        log.debug("getDeployments response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getDeployment(String ip, String namespace, String deploymentName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDeploymentService.getDeployment(ip, namespace, deploymentName);
        response.setData(result);
        log.debug("getDeployment response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getDeploymentYaml(String ip, String namespace, String deploymentName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sDeploymentService.getDeploymentYaml(ip, namespace, deploymentName);
        response.setData(result);
        log.debug("getDeploymentYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Deployment 管理", operation = "删除 Deployment", message = "ip=#{#ip}, namespace=#{#namespace}, deploymentName=#{#deploymentName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> deleteDeployment(String ip, String namespace, String deploymentName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDeploymentService.deleteDeployment(ip, namespace, deploymentName);
        response.setData(result);
        log.debug("deleteDeployment response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Deployment 管理", operation = "重启 Deployment", message = "ip=#{#ip}, namespace=#{#namespace}, deploymentName=#{#deploymentName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> restartDeployment(String ip, String namespace, String deploymentName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDeploymentService.restartDeployment(ip, namespace, deploymentName);
        response.setData(result);
        log.debug("restartDeployment response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Deployment 管理", operation = "更新副本数", message = "ip=#{#ip}, namespace=#{#namespace}, deploymentName=#{#deploymentName}, k8sUpdateReplica=#{#k8sUpdateReplica}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> updateDeploymentReplicas(String ip, String namespace, String deploymentName, K8sUpdateReplica k8sUpdateReplica) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDeploymentService.updateDeploymentReplicas(ip, namespace, deploymentName, k8sUpdateReplica);
        response.setData(result);
        log.debug("updateDeploymentReplicas response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Deployment 管理", operation = "调整镜像版本", message = "ip=#{#ip}, namespace=#{#namespace}, deploymentName=#{#deploymentName}, k8sUpdateImage=#{#k8sUpdateImage}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> updateDeploymentImages(String ip, String namespace, String deploymentName, K8sUpdateImage k8sUpdateImage) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDeploymentService.updateDeploymentImages(ip, namespace, deploymentName, k8sUpdateImage);
        response.setData(result);
        log.debug("updateDeploymentImages response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Deployment 管理", operation = "修改最大历史副本集数", message = "ip=#{#ip}, namespace=#{#namespace}, deploymentName=#{#deploymentName}, k8sUpdateRevisionHistoryLimit=#{#k8sUpdateRevisionHistoryLimit}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> updateDeploymentRevisionHistoryLimit(String ip, String namespace, String deploymentName, K8sUpdateRevisionHistoryLimit k8sUpdateRevisionHistoryLimit) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDeploymentService.updateDeploymentRevisionHistoryLimit(ip, namespace, deploymentName, k8sUpdateRevisionHistoryLimit);
        response.setData(result);
        log.debug("updateDeploymentRevisionHistoryLimit response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Deployment 管理", operation = "回滚", message = "ip=#{#ip}, namespace=#{#namespace}, deploymentName=#{#deploymentName}, k8sRollbackDeployment=#{#k8sRollbackDeployment}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> rollbackDeployment(String ip, String namespace, String deploymentName, K8sRollbackDeployment k8sRollbackDeployment) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDeploymentService.rollbackDeployment(ip, namespace, deploymentName, k8sRollbackDeployment);
        response.setData(result);
        log.debug("rollbackDeployment response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Deployment 管理", operation = "修改更新策略", message = "ip=#{#ip}, namespace=#{#namespace}, deploymentName=#{#deploymentName}, k8sUpdateDeploymentStrategy=#{#k8sUpdateDeploymentStrategy}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> updateDeploymentStrategy(String ip, String namespace, String deploymentName, K8sUpdateDeploymentStrategy k8sUpdateDeploymentStrategy) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDeploymentService.updateDeploymentStrategy(ip, namespace, deploymentName, k8sUpdateDeploymentStrategy);
        response.setData(result);
        log.debug("updateDeploymentStrategy response:{}", response);
        return response;
    }
}
