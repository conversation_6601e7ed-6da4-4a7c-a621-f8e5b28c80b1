package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sCustomObject;
import skynet.platform.manager.k8s.service.K8sCustomObjectService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sCustomObjectController implements V3K8sCustomObject {

    private final K8sCustomObjectService k8sCustomObjectService;

    public K8sCustomObjectController(K8sCustomObjectService k8sCustomObjectService) {
        this.k8sCustomObjectService = k8sCustomObjectService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getCustomObjects(String ip, String group, String version, String plural, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sCustomObjectService.getCustomObjects(ip, group, version, plural, k8sQuery);
        response.setData(results);
        log.debug("getCustomObjects response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getCustomObject(String ip, String namespace, String group, String version, String plural, String customObjectName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCustomObjectService.getCustomObject(ip, namespace, group, version, plural, customObjectName);
        response.setData(result);
        log.debug("getCustomObject response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getCustomObject(String ip, String group, String version, String plural, String customObjectName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sCustomObjectService.getCustomObject(ip, group, version, plural, customObjectName);
        response.setData(result);
        log.debug("getCustomObject response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getCustomObjectYaml(String ip, String namespace, String group, String version, String plural, String customObjectName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sCustomObjectService.getCustomObjectYaml(ip, namespace, group, version, plural, customObjectName);
        response.setData(result);
        log.debug("getCustomObjectYaml response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getCustomObjectYaml(String ip, String group, String version, String plural, String customObjectName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sCustomObjectService.getCustomObjectYaml(ip, group, version, plural, customObjectName);
        response.setData(result);
        log.debug("getCustomObjectYaml response:{}", response);
        return response;
    }

}
