package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sSecret;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sSecretService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sSecretController implements V3K8sSecret {

    private final K8sSecretService k8sSecretService;

    public K8sSecretController(K8sSecretService k8sSecretService) {
        this.k8sSecretService = k8sSecretService;
    }

    @Override
    public SkynetApiResponse<List<JSONObject>> getSecrets(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sSecretService.getSecrets(ip, k8sQuery);
        response.setData(results);
        log.debug("getSecrets response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<JSONObject> getSecret(String ip, String namespace, String secretName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sSecretService.getSecret(ip, namespace, secretName);
        response.setData(result);
        log.debug("getSecret response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<String> getSecretYaml(String ip, String namespace, String secretName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sSecretService.getSecretYaml(ip, namespace, secretName);
        response.setData(result);
        log.debug("getSecretYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S Secret 管理", operation = "删除 Secret", message = "ip=#{#ip}, namespace=#{#namespace}, secretName=#{#secretName}")
    public SkynetApiResponse<JSONObject> deleteSecret(String ip, String namespace, String secretName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sSecretService.deleteSecret(ip, namespace, secretName);
        response.setData(result);
        log.debug("deleteSecret response:{}", response);
        return response;
    }

}
