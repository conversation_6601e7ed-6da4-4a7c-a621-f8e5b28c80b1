package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sUpdateDaemonSetStrategy;
import skynet.platform.feign.model.K8sUpdateImage;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sDaemonSet;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sDaemonSetService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sDaemonSetController implements V3K8sDaemonSet {

    private final K8sDaemonSetService k8sDaemonSetService;

    public K8sDaemonSetController(K8sDaemonSetService k8sDaemonSetService) {
        this.k8sDaemonSetService = k8sDaemonSetService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getDaemonSets(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sDaemonSetService.getDaemonSets(ip, k8sQuery);
        response.setData(results);
        log.debug("getDaemonSets response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getDaemonSet(String ip, String namespace, String daemonSetName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDaemonSetService.getDaemonSet(ip, namespace, daemonSetName);
        response.setData(result);
        log.debug("getDaemonSet response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getDaemonSetYaml(String ip, String namespace, String daemonSetName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sDaemonSetService.getDaemonSetYaml(ip, namespace, daemonSetName);
        response.setData(result);
        log.debug("getDaemonSetYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S DaemonSet 管理", operation = "删除 DaemonSet", message = "ip=#{#ip}, namespace=#{#namespace}, daemonSetName=#{#daemonSetName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> deleteDaemonSet(String ip, String namespace, String daemonSetName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDaemonSetService.deleteDaemonSet(ip, namespace, daemonSetName);
        response.setData(result);
        log.debug("deleteDaemonSet response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S DaemonSet 管理", operation = "重启 DaemonSet", message = "ip=#{#ip}, namespace=#{#namespace}, daemonSetName=#{#daemonSetName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> restartDaemonSet(String ip, String namespace, String daemonSetName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDaemonSetService.restartDaemonSet(ip, namespace, daemonSetName);
        response.setData(result);
        log.debug("restartDaemonSet response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S DaemonSet 管理", operation = "调整镜像版本", message = "ip=#{#ip}, namespace=#{#namespace}, daemonSetName=#{#daemonSetName}, k8sUpdateImage=#{#k8sUpdateImage}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> updateDaemonSetImages(String ip, String namespace, String daemonSetName, K8sUpdateImage k8sUpdateImage) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDaemonSetService.updateDaemonSetImages(ip, namespace, daemonSetName, k8sUpdateImage);
        response.setData(result);
        log.debug("updateDaemonSetImages response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S DaemonSet 管理", operation = "修改更新策略", message = "ip=#{#ip}, namespace=#{#namespace}, daemonSetName=#{#daemonSetName}, k8sUpdateDaemonSetStrategy=#{#k8sUpdateDaemonSetStrategy}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> updateDaemonSetStrategy(String ip, String namespace, String daemonSetName, K8sUpdateDaemonSetStrategy k8sUpdateDaemonSetStrategy) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sDaemonSetService.updateDaemonSetStrategy(ip, namespace, daemonSetName, k8sUpdateDaemonSetStrategy);
        response.setData(result);
        log.debug("updateDaemonSetStrategy response:{}", response);
        return response;
    }

}
