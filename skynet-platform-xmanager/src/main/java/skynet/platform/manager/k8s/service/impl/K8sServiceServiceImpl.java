package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Service;
import io.kubernetes.client.openapi.models.V1ServiceList;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sServiceService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sServiceServiceImpl extends K8sBaseService implements K8sServiceService {

    public K8sServiceServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Service 列表
     */
    @Override
    public List<JSONObject> getServices(String ip, K8sQuery k8sQuery) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        List<JSONObject> serviceDtoList = new ArrayList<>();
        V1ServiceList serviceList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            serviceList = api.listNamespacedService(k8sQuery.getNamespace()).execute();
        } else {
            serviceList = api.listServiceForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1Service> filteredServices = applyK8sQueryFilter(serviceList.getItems(), k8sQuery);

        for (V1Service service : filteredServices) {
            serviceDtoList.add(toJSON(service));
        }
        return serviceDtoList;
    }

    /**
     * 获取 Service 详情
     */
    @Override
    public JSONObject getService(String ip, String namespace, String serviceName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Service service = api.readNamespacedService(serviceName, namespace).execute();
        return toJSON(service);
    }

    /**
     * 获取 Service Yaml
     */
    @Override
    public String getServiceYaml(String ip, String namespace, String serviceName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Service service = api.readNamespacedService(serviceName, namespace).execute();
        return Yaml.dump(service);
    }

    /**
     * 删除 Service
     */
    @Override
    public JSONObject deleteService(String ip, String namespace, String serviceName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Service service = api.deleteNamespacedService(serviceName, namespace).execute();
        return toJSON(service);
    }

}
