package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.models.V1Pod;
import io.kubernetes.client.openapi.models.V1PodList;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sPodService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sPodServiceImpl extends K8sBaseService implements K8sPodService {

    public K8sPodServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Pod 列表
     */
    @Override
    public List<JSONObject> getPods(String ip, K8sQuery k8sQuery) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        List<JSONObject> podDtoList = new ArrayList<>();
        V1PodList podList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            podList = api.listNamespacedPod(k8sQuery.getNamespace()).execute();
        } else {
            podList = api.listPodForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1Pod> filteredPods = applyK8sQueryFilter(podList.getItems(), k8sQuery);

        for (V1Pod pod : filteredPods) {
            podDtoList.add(toJSON(pod));
        }
        return podDtoList;
    }

    /**
     * 获取 Pod 详情
     */
    @Override
    public JSONObject getPod(String ip, String namespace, String podName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Pod pod = api.readNamespacedPod(podName, namespace).execute();
        return toJSON(pod);
    }

    /**
     * 获取 Pod Yaml
     */
    @Override
    public String getPodYaml(String ip, String namespace, String podName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Pod pod = api.readNamespacedPod(podName, namespace).execute();
        return Yaml.dump(pod);
    }

    /**
     * 删除 Pod
     */
    @Override
    public JSONObject deletePod(String ip, String namespace, String podName) throws Exception {

        CoreV1Api api = new CoreV1Api(initApiClient(ip));

        V1Pod pod = api.deleteNamespacedPod(podName, namespace).execute();
        return toJSON(pod);
    }

}
