package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sReplicaSet;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sReplicaSetService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sReplicaSetController implements V3K8sReplicaSet {

    private final K8sReplicaSetService k8sReplicaSetService;

    public K8sReplicaSetController(K8sReplicaSetService k8sReplicaSetService) {
        this.k8sReplicaSetService = k8sReplicaSetService;
    }

    @Override
    public SkynetApiResponse<List<JSONObject>> getReplicaSets(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sReplicaSetService.getReplicaSets(ip, k8sQuery);
        response.setData(results);
        log.debug("getReplicaSets response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<JSONObject> getReplicaSet(String ip, String namespace, String replicaSetName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sReplicaSetService.getReplicaSet(ip, namespace, replicaSetName);
        response.setData(result);
        log.debug("getReplicaSet response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<String> getReplicaSetYaml(String ip, String namespace, String replicaSetName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sReplicaSetService.getReplicaSetYaml(ip, namespace, replicaSetName);
        response.setData(result);
        log.debug("getReplicaSetYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S ReplicaSet 管理", operation = "删除 ReplicaSet", message = "ip=#{#ip}, namespace=#{#namespace}, replicaSetName=#{#replicaSetName}")
    public SkynetApiResponse<JSONObject> deleteReplicaSet(String ip, String namespace, String replicaSetName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sReplicaSetService.deleteReplicaSet(ip, namespace, replicaSetName);
        response.setData(result);
        log.debug("deleteReplicaSet response:{}", response);
        return response;
    }

}
