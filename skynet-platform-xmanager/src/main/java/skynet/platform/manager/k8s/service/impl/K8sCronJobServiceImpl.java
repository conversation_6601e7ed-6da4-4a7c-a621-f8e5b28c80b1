package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.custom.V1Patch;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.apis.BatchV1Api;
import io.kubernetes.client.openapi.models.*;
import io.kubernetes.client.util.PatchUtils;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sUpdateCronJobHistoryLimit;
import skynet.platform.manager.admin.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sCronJobService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Kubernetes CronJob 服务实现类
 * <p>
 * 核心功能：
 * 1. CronJob 生命周期管理
 * - 定时任务创建/更新/删除
 * - 任务状态管理（暂停/恢复）
 * - 手工触发立即执行
 * <p>
 * 2. 历史任务管理
 * - 成功/失败任务保留策略
 * - 历史记录清理配置
 * <p>
 * 3. 集群兼容性
 * - 支持 Kubernetes 24.0.0+ 版本API
 */
@Slf4j
@Service
public class K8sCronJobServiceImpl extends K8sBaseService implements K8sCronJobService {

    public K8sCronJobServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 CronJob 列表
     */
    @Override
    public List<JSONObject> getCronJobs(String ip, K8sQuery k8sQuery) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        List<JSONObject> cronJobDtoList = new ArrayList<>();
        V1CronJobList cronJobList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            cronJobList = api.listNamespacedCronJob(k8sQuery.getNamespace()).execute();
        } else {
            cronJobList = api.listCronJobForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1CronJob> filteredCronJobs = applyK8sQueryFilter(cronJobList.getItems(), k8sQuery);

        for (V1CronJob cronJob : filteredCronJobs) {
            cronJobDtoList.add(toJSON(cronJob));
        }
        return cronJobDtoList;
    }

    /**
     * 获取 CronJob 详情
     */
    @Override
    public JSONObject getCronJob(String ip, String namespace, String cronJobName) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        V1CronJob cronJob = api.readNamespacedCronJob(cronJobName, namespace).execute();
        return toJSON(cronJob);
    }

    /**
     * 获取 CronJob Yaml
     */
    @Override
    public String getCronJobYaml(String ip, String namespace, String cronJobName) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        V1CronJob cronJob = api.readNamespacedCronJob(cronJobName, namespace).execute();
        return Yaml.dump(cronJob);
    }

    /**
     * 删除 CronJob
     */
    @Override
    public JSONObject deleteCronJob(String ip, String namespace, String cronJobName) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        V1Status status = api.deleteNamespacedCronJob(cronJobName, namespace).execute();
        return toJSON(status);
    }

    /**
     * 暂停定时任务
     * <p>
     * 实现原理：
     * - 通过JSON Patch修改spec.suspend字段
     * - 修改立即生效无需重启
     *
     * @param ip          集群节点IP地址
     * @param namespace   任务所在命名空间
     * @param cronJobName 定时任务名称
     * @return 更新后的任务状态（JSON格式）
     */
    @Override
    public JSONObject stopCronJob(String ip, String namespace, String cronJobName) throws Exception {
        ApiClient apiClient = initApiClient(ip);
        BatchV1Api api = new BatchV1Api(apiClient);

        // json-patch a cronjob
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
                .fluentPut("op", "replace")
                .fluentPut("path", "/spec/suspend")
                .fluentPut("value", true));

        V1Patch patchBody = new V1Patch(patch.toString());
//        V1CronJob cronJob = api.patchNamespacedCronJob(cronJobName, namespace, patchBody).execute();

        V1CronJob cronJob = PatchUtils.patch(
                V1CronJob.class,
                () -> api.patchNamespacedCronJob(cronJobName, namespace, patchBody).buildCall(null),
                V1Patch.PATCH_FORMAT_JSON_PATCH,
                apiClient
        );

        return toJSON(cronJob);
    }

    /**
     * 恢复运行
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 BatchV1Api.patchNamespacedCronJob 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     *
     * @param ip          集群节点IP地址
     * @param namespace   命名空间
     * @param cronJobName CronJob 名称
     * @return 恢复后的 CronJob 对象
     * @throws Exception 当恢复失败时抛出
     */
    @Override
    public JSONObject resumeCronJob(String ip, String namespace, String cronJobName) throws Exception {
        ApiClient apiClient = initApiClient(ip);
        BatchV1Api api = new BatchV1Api(apiClient);

        // json-patch a cronjob
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
                .fluentPut("op", "replace")
                .fluentPut("path", "/spec/suspend")
                .fluentPut("value", false));

        V1Patch patchBody = new V1Patch(patch.toString());
//        V1CronJob cronJob = api.patchNamespacedCronJob(cronJobName, namespace, patchBody).execute();
        V1CronJob cronJob = PatchUtils.patch(
                V1CronJob.class,
                () -> api.patchNamespacedCronJob(cronJobName, namespace, patchBody).buildCall(null),
                V1Patch.PATCH_FORMAT_JSON_PATCH,
                apiClient
        );
        return toJSON(cronJob);
    }

    /**
     * 手工触发，根据 CronJob 创建一个 Job
     */
    @Override
    public JSONObject manualCronJob(String ip, String namespace, String cronJobName) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        V1CronJob cronJob = api.readNamespacedCronJob(cronJobName, namespace).execute();
        V1Job job = buildJob(cronJob);
        job = api.createNamespacedJob(namespace, job).execute();
        return toJSON(job);
    }

    private V1Job buildJob(V1CronJob cronJob) {
        V1Job job = new V1Job();
        job.setKind("Job");
        job.setApiVersion("batch/v1");
        job.setMetadata(buildJobMetadata(cronJob));
        job.setSpec(buildJobSpec(cronJob));
        return job;
    }

    private V1ObjectMeta buildJobMetadata(V1CronJob cronJob) {
        V1ObjectMeta metadata = new V1ObjectMeta();
        metadata.setName(String.format("%s-manual-%s", cronJob.getMetadata().getName(), RandomStringUtils.randomAlphanumeric(4).toLowerCase()));
        metadata.setNamespace(cronJob.getMetadata().getNamespace());
        metadata.setAnnotations(buildJobAnnotations());
        metadata.setOwnerReferences(buildOwnerReferences(cronJob));
        return metadata;
    }

    private Map<String, String> buildJobAnnotations() {
        Map<String, String> annotations = new HashMap<>();
        annotations.put("cronjob.kubernetes.io/instantiate", "manual");
        return annotations;
    }

    private List<V1OwnerReference> buildOwnerReferences(V1CronJob cronJob) {
        V1OwnerReference ownerReference = new V1OwnerReference();
        ownerReference.setKind("CronJob");
        ownerReference.setApiVersion("batch/v1");
        ownerReference.setController(true);
        ownerReference.setName(cronJob.getMetadata().getName());
        ownerReference.setUid(cronJob.getMetadata().getUid());
        List<V1OwnerReference> ownerReferences = new ArrayList<>();
        ownerReferences.add(ownerReference);
        return ownerReferences;
    }

    private V1JobSpec buildJobSpec(V1CronJob cronJob) {
        V1JobSpec jobSpec = new V1JobSpec();
        jobSpec.setTemplate(cronJob.getSpec().getJobTemplate().getSpec().getTemplate());
        return jobSpec;
    }

    /**
     * 更新最大历史 Job 数
     * <p>
     * 升级说明：
     * - 移除已弃用的 PatchUtils.patch 方法
     * - 直接使用 BatchV1Api.patchNamespacedCronJob 方法
     * - 在 24.0.0 版本中推荐直接调用 API 方法
     * - 同时更新成功和失败的历史 Job 数量限制
     *
     * @param ip                           集群节点IP地址
     * @param namespace                    命名空间
     * @param cronJobName                  CronJob 名称
     * @param k8sUpdateCronJobHistoryLimit 历史 Job 数量限制更新信息
     * @return 更新后的 CronJob 对象
     * @throws Exception 当更新失败时抛出
     */
    @Override
    public JSONObject updateCronJobHistoryLimit(String ip, String namespace, String cronJobName, K8sUpdateCronJobHistoryLimit k8sUpdateCronJobHistoryLimit) throws Exception {
        ApiClient apiClient = initApiClient(ip);
        BatchV1Api api = new BatchV1Api(apiClient);

        // json-patch a cronjob
        JSONArray patch = new JSONArray();
        patch.add(new JSONObject()
                .fluentPut("op", "replace")
                .fluentPut("path", "/spec/successfulJobsHistoryLimit")
                .fluentPut("value", k8sUpdateCronJobHistoryLimit.getSuccessfulJobsHistoryLimit()));
        patch.add(new JSONObject()
                .fluentPut("op", "replace")
                .fluentPut("path", "/spec/failedJobsHistoryLimit")
                .fluentPut("value", k8sUpdateCronJobHistoryLimit.getFailedJobsHistoryLimit()));

        V1Patch patchBody = new V1Patch(patch.toString());
//        V1CronJob cronJob = api.patchNamespacedCronJob(cronJobName, namespace, patchBody).execute();

        V1CronJob cronJob = PatchUtils.patch(
                V1CronJob.class,
                () -> api.patchNamespacedCronJob(cronJobName, namespace, patchBody).buildCall(null),
                V1Patch.PATCH_FORMAT_JSON_PATCH,
                apiClient
        );
        return toJSON(cronJob);
    }
}
