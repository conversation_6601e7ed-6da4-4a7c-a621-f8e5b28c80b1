package skynet.platform.manager.k8s.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sKubectl;
import skynet.platform.manager.admin.service.V3AgentService;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.impl.K8sKubectlService;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sKubectlController implements V3K8sKubectl {

    private final K8sKubectlService k8SKubectlService;
    private final V3AgentService v3AgentService;

    public K8sKubectlController(K8sKubectlService k8SKubectlService, V3AgentService v3AgentService) {
        this.k8SKubectlService = k8SKubectlService;
        this.v3AgentService = v3AgentService;
    }

    @Override
    @AuditLog(module = "K8S Kubectl", operation = "执行 kubectl apply 命令", message = "ip=#{#ip}, yamlText=#{#yamlText}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<String> apply(String ip, String yamlText) throws Exception {

        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        AgentDto agentDto = v3AgentService.getAgent(ip);
        k8SKubectlService.apply(ip, agentDto.getKubeConfig(), yamlText);
        response.setData("ok");
        log.debug("apply response:{}", response);
        return response;

    }
}
