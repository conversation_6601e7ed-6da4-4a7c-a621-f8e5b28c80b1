package skynet.platform.manager.k8s.controller;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sConfigMap;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sConfigMapService;

import java.util.List;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sConfigMapController implements V3K8sConfigMap {

    private final K8sConfigMapService k8sConfigMapService;

    public K8sConfigMapController(K8sConfigMapService k8sConfigMapService) {
        this.k8sConfigMapService = k8sConfigMapService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<JSONObject>> getConfigMaps(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sConfigMapService.getConfigMaps(ip, k8sQuery);
        response.setData(results);
        log.debug("getConfigMaps response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<JSONObject> getConfigMap(String ip, String namespace, String configMapName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sConfigMapService.getConfigMap(ip, namespace, configMapName);
        response.setData(result);
        log.debug("getConfigMap response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getConfigMapYaml(String ip, String namespace, String configMapName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sConfigMapService.getConfigMapYaml(ip, namespace, configMapName);
        response.setData(result);
        log.debug("getConfigMapYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S ConfigMap 管理", operation = "删除 ConfigMap", message = "ip=#{#ip}, namespace=#{#namespace}, configMapName=#{#configMapName}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<JSONObject> deleteConfigMap(String ip, String namespace, String configMapName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sConfigMapService.deleteConfigMap(ip, namespace, configMapName);
        response.setData(result);
        log.debug("deleteConfigMap response:{}", response);
        return response;
    }

}
