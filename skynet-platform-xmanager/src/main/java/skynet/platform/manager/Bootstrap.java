package skynet.platform.manager;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import skynet.boot.AppUtils;
import skynet.boot.annotation.*;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.manager.server.AntManagerApp;

/**
 * Skynet管理控制台启动类
 *
 * <p>这是Skynet平台管理控制台(xmanager)的主启动类，负责初始化和启动整个管理控制台应用。
 * 该应用提供了Skynet平台的Web管理界面和REST API接口，是平台管理的核心组件。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>服务定义管理 - 创建、编辑、删除服务定义</li>
 *   <li>节点管理 - 管理Agent节点和服务器资源</li>
 *   <li>服务运行时管理 - 启动、停止、重启服务实例</li>
 *   <li>集群监控 - 监控集群状态和服务健康度</li>
 *   <li>配置管理 - 管理系统配置和服务配置</li>
 *   <li>用户认证和权限管理</li>
 * </ul>
 *
 * <p>启用的功能模块：</p>
 * <ul>
 *   <li>Servlet组件扫描 - 自动发现和注册Servlet组件</li>
 *   <li>异步处理 - 支持异步方法调用和任务执行</li>
 *   <li>WebSocket - 提供实时通信能力</li>
 *   <li>安全框架 - 集成认证和授权机制</li>
 *   <li>API文档 - 自动生成Swagger API文档</li>
 *   <li>服务发现 - 集成服务注册和发现功能</li>
 *   <li>异常处理 - 统一的异常处理机制</li>
 * </ul>
 *
 * <p>配置文件加载：</p>
 * <ul>
 *   <li>加载classpath下所有spring/manager/目录中的XML配置文件</li>
 *   <li>支持多环境配置文件</li>
 * </ul>
 *
 * <AUTHOR> [2018年8月10日 下午11:56:04]
 * @since 3.4.15
 * @see AntManagerApp 管理应用核心类
 * @see AppBootEnvironment 应用启动环境
 */
@ServletComponentScan
@EnableAsync
@EnableSkynetWebSocket
@EnableSkynetSecurity
@EnableSkynetSwagger2
@EnableSkynetDiscoveryClient
@EnableSkynetException
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true)
@ImportResource(locations = {"classpath*:spring/manager/*.xml"})
@SpringBootApplication
public class Bootstrap {

    /**
     * 应用程序主入口方法
     *
     * <p>初始化Skynet管理控制台应用并启动Spring Boot容器。
     * 在启动过程中会初始化应用启动环境，设置必要的系统属性，
     * 然后启动Spring Boot应用容器。</p>
     *
     * <p>启动流程：</p>
     * <ol>
     *   <li>初始化应用启动环境，设置Action Point</li>
     *   <li>加载配置文件和环境变量</li>
     *   <li>启动Spring Boot应用容器</li>
     *   <li>初始化各个功能模块</li>
     *   <li>打印应用启动信息和服务详情</li>
     * </ol>
     *
     * @param args 命令行启动参数，支持Spring Boot标准参数格式
     *             常用参数包括：
     *             --server.port=端口号
     *             --spring.profiles.active=环境名
     *             --skynet.zookeeper.server_list=ZK地址
     * @throws Exception 当应用启动失败时抛出异常，包括配置错误、端口占用、依赖服务不可用等情况
     */
    public static void main(String[] args) throws Exception {
        // 初始化应用启动环境，设置管理控制台的Action Point标识
        AppBootEnvironment.init(AntManagerApp.ACTION_POINT);

        // 启动Spring Boot应用并打印服务详情
        AppUtils.run(Bootstrap.class, args);
    }
}
