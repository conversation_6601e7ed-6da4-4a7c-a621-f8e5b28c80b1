package skynet.platform.manager.security;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.util.Assert;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/21 12:43
 */
@Slf4j
public class SkynetZkUserService implements UserDetailsService {

    private static final String PW_PATTERN = "^(?![A-Za-z0-9]+$)(?![a-z0-9\\W]+$)(?![A-Za-z\\W]+$)(?![A-Z0-9\\W]+$)[a-zA-Z0-9\\W]{8,}$";
    private final IAntConfigService antConfigService;

    private final SkynetPasswordEncoder skynetPasswordEncoder;

    public SkynetZkUserService(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
        this.skynetPasswordEncoder = new SkynetPasswordEncoder();
    }

    public User findByUsername(String username) {
        log.debug("check username:{}", username);
        Assert.hasText(username, "username is blank.");

        String newUserPath = String.format("/%s/users/%s", antConfigService.getClusterName(), username);
        String oldUserPath = String.format("/%s/xmanager/users/%s", antConfigService.getClusterName(), username);

        String encodedPassword = null;
        String rolesStr = "viewer"; // 默认角色

        if (antConfigService.exists(newUserPath)) {
            String userData = antConfigService.getData(newUserPath);
            if (StringUtils.isNotBlank(userData)) {
                JSONObject userJson = JSON.parseObject(userData);
                encodedPassword = userJson.getString("pwd");
                rolesStr = userJson.getString("roles");
            }
        } else if (antConfigService.exists(oldUserPath)) {
            encodedPassword = antConfigService.getData(oldUserPath);
        } else {
            throw new UsernameNotFoundException("User not found: " + username);
        }

        if (StringUtils.isBlank(encodedPassword)) {
            throw new UsernameNotFoundException("User password not found: " + username);
        }

        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        if (StringUtils.isNotBlank(rolesStr)) {
            Arrays.stream(rolesStr.split(","))
                    .map(String::trim)
                    .map(role -> new SimpleGrantedAuthority("ROLE_" + role.toUpperCase()))
                    .forEach(grantedAuthorities::add);
        }

        return new User(username, encodedPassword, grantedAuthorities);
    }


    public void addUser(String username, String pw) {
        log.info("addUser userName:{}", username);
        Assert.hasText(username, "username is blank.");
        Assert.hasText(pw, "pw is blank.");

        //正则校验新密码，8位以上包含大小写字母，数字，特殊字符
        if (!pw.matches(PW_PATTERN)) {
            throw new ApiRequestException(ApiRequestErrorCode.USER_PASSWORD_INVALID);
        }

        String path = String.format("/%s/users/%s", antConfigService.getClusterName(), username);
        if (antConfigService.exists(path)) {
            throw new ApiRequestException(ApiRequestErrorCode.USER_ALREADY_EXIST);
        }

        String encodedPassword = skynetPasswordEncoder.encode(pw);
        JSONObject userData = new JSONObject();
        userData.put("pwd", encodedPassword);
        userData.put("roles", "viewer"); // 默认角色

        // 修改密码
        antConfigService.setData(path, userData.toJSONString());
        log.info("AddUser OK.[username = {}]", username);
    }


    public void updatePwd(String username, String pw, String newPw) {
        log.info("updatePwd username:{}", username);
        Assert.hasText(username, "username is blank.");
        Assert.hasText(pw, "password is blank.");
        Assert.hasText(newPw, "newPassword is blank.");

        //正则校验新密码，8位以上包含大小写字母，数字，特殊字符
        if (!newPw.matches(PW_PATTERN)) {
            throw new ApiRequestException(ApiRequestErrorCode.USER_PASSWORD_INVALID);
        }

        String newUserPath = String.format("/%s/users/%s", antConfigService.getClusterName(), username);
        String oldUserPath = String.format("/%s/xmanager/users/%s", antConfigService.getClusterName(), username);

        String encodedPassword;
        String path;
        JSONObject userData = null;

        if (antConfigService.exists(newUserPath)) {
            path = newUserPath;
            String data = antConfigService.getData(path);
            userData = JSON.parseObject(data);
            encodedPassword = userData.getString("pwd");
        } else if (antConfigService.exists(oldUserPath)) {
            path = oldUserPath;
            encodedPassword = antConfigService.getData(path);
        } else {
            throw new ApiRequestException(ApiRequestErrorCode.USER_AUTH_INVALID);
        }

        if (!skynetPasswordEncoder.matches(pw, encodedPassword)) {
            throw new ApiRequestException(ApiRequestErrorCode.USER_AUTH_INVALID);
        }

        String newEncodedPassword = skynetPasswordEncoder.encode(newPw);

        if (userData != null) {
            userData.put("pwd", newEncodedPassword);
            antConfigService.setData(path, userData.toJSONString());
        } else {
            // 如果是旧用户，迁移到新格式
            antConfigService.delData(oldUserPath);
            JSONObject newUserJson = new JSONObject();
            newUserJson.put("pwd", newEncodedPassword);
            newUserJson.put("roles", "viewer");
            antConfigService.setData(newUserPath, newUserJson.toJSONString());
        }

        log.info("Update pw OK.[username = {}]", username);
    }

    public void resetPwd(String username, String password) {
        log.info("ResetPwd username:{}", username);
        Assert.hasText(username, "username is blank.");
        Assert.hasText(password, "password is blank.");

        //正则校验新密码，8位以上包含大小写字母，数字，特殊字符
        if (!password.matches(PW_PATTERN)) {
            throw new ApiRequestException(ApiRequestErrorCode.USER_PASSWORD_INVALID);
        }

        String path = String.format("/%s/users/%s", antConfigService.getClusterName(), username);
        if (!antConfigService.exists(path)) {
            throw new ApiRequestException(ApiRequestErrorCode.USER_NOT_EXIST);
        }

        String encodedPassword = skynetPasswordEncoder.encode(password);
        JSONObject userData = new JSONObject();
        userData.put("pwd", encodedPassword);
        userData.put("roles", "viewer"); // 默认角色

        // 修改密码
        antConfigService.setData(path, userData.toJSONString());
        log.info("Rest Password OK.[username = {}]", username);
    }

    public SkynetPasswordEncoder getPasswordEncoder() {
        return skynetPasswordEncoder;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {

        User user = this.findByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("the user does not exist");
        }
        return user;
    }
}
