package skynet.platform.manager.audit;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import skynet.boot.common.domain.Jsonable;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.List;


@Service
@ConditionalOnProperty(value = "skynet.audit.test.enabled")
class AuditLogTest {

    @AuditLog(module = "TEST", operation = "操作", message = "无参数测试")
    public void test1() {

    }

    @AuditLog(module = "TEST", operation = "操作", message = "无参数测试-直接异常")
    public void test2() throws Exception {
        throw new Exception("throw new exception");
    }

    @AuditLog(module = "TEST", operation = "操作", message = "#user")
    public void test30(LoginInfo user) {
    }

    @AuditLog(module = "TEST", operation = "操作", message = "有参数测试-参数为对象-属性为空:#{#user.username}")
    public void test31(LoginInfo user) {

    }

    @AuditLog(module = "TEST", operation = "操作", message = "有参数测试-参数为对象-属性正常:#{#user.password}")
    public void test32(LoginInfo user) {

    }

    @AuditLog(module = "TEST", operation = "操作", message = "有参数测试-参数基本类型-为空")
    public void test40(String ip) {

    }

    @AuditLog(module = "TEST", operation = "操作", message = "有参数测试-参数基本类型-正常:#{#ip}")
    public void test41(String ip) {

    }

    @AuditLog(module = "TEST", operation = "操作", message = "有参数测试-参数基本类型-为空")
    public void test50(List<LoginInfo> userList) {

    }

    @AuditLog(module = "TEST", operation = "操作", message = "有参数测试-参数基本类型-正常:#{#userList[0].username}")
    public void test51(List<LoginInfo> userList) {

    }

    @Getter
    @Setter
    public static class LoginInfo extends Jsonable {

        /**
         * 登录名
         */
        private String username;

        /**
         * 密码
         */
        private String password;

        @Override
        public String toString() {
            return super.toString();
        }
    }
}
