package skynet.platform.manager.admin.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3ActionTag;
import skynet.platform.manager.admin.service.V3ActionTagService;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.List;

/**
 * <AUTHOR> by jianwu6 on 2020/8/18 16:56
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2

public class V3ActionTagController implements V3ActionTag {

    private final V3ActionTagService v3ActionTagService;

    public V3ActionTagController(V3ActionTagService v3ActionTagService) {
        this.v3ActionTagService = v3ActionTagService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<String>> getTags() {
        SkynetApiResponse<List<String>> response = new SkynetApiResponse<>();
        try {
            List<String> tags = v3ActionTagService.getTags();
            response.setData(tags);
        } catch (Exception e) {
            log.error("getTags error. ", e);
            response.setException(e);
        }
        log.debug("getTags response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "服务标签", operation = "更新服务标签", message = "update action tags,tags = #{#tags}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> updateTags(@RequestBody List<String> tags) {
        SkynetApiResponse<Void> response = new SkynetApiResponse<>();
        try {
            v3ActionTagService.updateTags(tags);
        } catch (Exception e) {
            log.error("updateTags error. ", e);
            response.setException(e);
        }
        log.debug("updateTags response:{}", response);
        return response;
    }
}
