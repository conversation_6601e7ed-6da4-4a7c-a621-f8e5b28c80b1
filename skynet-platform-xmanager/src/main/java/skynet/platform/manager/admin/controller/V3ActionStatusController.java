package skynet.platform.manager.admin.controller;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.exception.ActionNotExistException;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.ActionRebootDto;
import skynet.platform.feign.model.ActionStatusUpdateDto;
import skynet.platform.feign.model.NoDataResponse;
import skynet.platform.feign.service.V3ActionStatus;
import skynet.platform.manager.admin.service.V3ActionStatusService;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2

public class V3ActionStatusController implements V3ActionStatus {

    private final V3ActionStatusService v3ActionStatusService;

    public V3ActionStatusController(V3ActionStatusService v3ActionStatusService) {
        this.v3ActionStatusService = v3ActionStatusService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public GetAllStatusResponse getAllStatus(@Parameter(description = "过滤条件：服务所在ip") @RequestParam(required = false, name = "ip") String ip) {
        log.debug("getAllStatus. ip={}", ip);
        GetAllStatusResponse response = new GetAllStatusResponse();
        try {
            response.setData(v3ActionStatusService.getAllStatus(ip));
        } catch (Exception e) {
            log.error("getAllStatus error. ip={}; error={}", ip, e instanceof ActionNotExistException ? e.getMessage() : e);
            response.setException(e);
        }
        log.debug("getAllStatus response={}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public GetAllStatusResponse getStatus(
            @Parameter(description = "服务坐标") @PathVariable String actionPoint,
            @Parameter(description = "过滤条件：服务所在ip") @RequestParam(required = false, name = "ip") String ip) {

        log.debug("getStatus. actionPoint={} ip={}", actionPoint, ip);
        GetAllStatusResponse response = new GetAllStatusResponse();
        try {
            response.setData(v3ActionStatusService.getStatus(actionPoint, ip));
        } catch (Exception e) {
            log.error("getStatus error. actionPoint={} ip={}; error={}", actionPoint, ip, e instanceof ActionNotExistException ? e.getMessage() : e);
            response.setException(e);
        }
        log.debug("getStatus response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "服务状态", operation = "更新服务状态", message = "param=#{#request}")
    @PreAuthorize("hasRole('ADMIN')")
    public NoDataResponse updateInstanceStatus(@RequestBody List<ActionStatusUpdateDto> request) {

        log.debug("updateInstanceStatus,param={} ", request);
        NoDataResponse response = new NoDataResponse();
        try {
            if (request == null) {
                throw new ApiRequestException(ApiRequestErrorCode.MISSING_PARAMETER);
            }

            // 根据ip分组，进行批量更新， by lyhu 2024年12月05日21:45:26
            // 转换成 Map<String, List<ActionStatusUpdateDto>>
            Map<String, List<ActionStatusUpdateDto>> ipMap = request.stream()
                    .collect(Collectors.groupingBy(ActionStatusUpdateDto::getIp));

            for (String ip : ipMap.keySet()) {
                v3ActionStatusService.update(ip, ipMap.get(ip));
            }
        } catch (Exception e) {
            log.error("updateInstanceStatus error. request={} error={}", request, e instanceof ActionNotExistException ? e.getMessage() : e);
            response.setException(e);
        }
        log.debug("updateInstanceStatus response={}", response);
        return response;
    }


    @Override
    @AuditLog(module = "服务状态", operation = "重启服务", message = "param=#{#request}")
    @PreAuthorize("hasRole('ADMIN')")
    public RebootResponse reboot(@RequestBody List<ActionRebootDto> request) {

        log.debug("Reboot request={} ", request);
        RebootResponse response = new RebootResponse();

        List<String> successIPList = new ArrayList<>();
        response.setData(successIPList);
        for (ActionRebootDto dto : request) {
            try {
                if (dto.getActionIdList() != null && dto.getActionIdList().size() > 0) {
                    v3ActionStatusService.reboot(dto.getIp(), dto.getActionIdList());
                }
                if (dto.getActionPointList() != null && dto.getActionPointList().size() > 0) {
                    v3ActionStatusService.rebootByActionPoint(dto.getIp(), dto.getActionPointList());
                }
                if ((dto.getActionIdList().size() + dto.getActionPointList().size()) == 0) {
                    log.warn("The reboot dto actionId or actionPoint is blank.[dto={}]", dto);
                }
                successIPList.add(dto.getIp());
            } catch (Exception e) {
                log.error("Reboot {}, Error={}", dto, e);
                response.setException(e);
            }
        }
        log.debug("Reboot {}, response={}", request, response);
        return response;
    }
}
