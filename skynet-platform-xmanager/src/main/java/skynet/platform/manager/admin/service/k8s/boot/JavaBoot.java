package skynet.platform.manager.admin.service.k8s.boot;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import skynet.boot.SkynetConsts;
import skynet.boot.SkynetProperties;
import skynet.platform.common.domain.BootParam;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.env.BootEnvironmentBuilder;
import skynet.platform.common.repository.domain.AntActionLabel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component(JavaBoot.BEAN_NAME)
public class JavaBoot extends BaseBoot {

    public static final String BEAN_NAME = "boot.javaboot";

    private static final String JAVA_BOOT_OPTS_KEY = "skynet.java.boot.default.javaopts";
    private static final String DEFAULT_JAVA_BOOT_OPTS = "-Xms128M -Xmx2G";
    private final Environment agentEnvironment;

    /**
     * skynet 给托管的java服务 增加启动属性，
     * 使用场景 如zk鉴权，需要在启动命令行上增加 java.security.auth.login.config
     * 目前在 conf/application.properties 中进行了配置，
     */
    @Value("#{'${skynet.append.jvm.options.props:}'.split(',')}")
    private List<String> appendJvmOptionsProperties;

    public JavaBoot(SkynetProperties skynetProperties, BootEnvironmentBuilder bootEnvironmentBuilder, Environment environment) {
        super(skynetProperties, bootEnvironmentBuilder);
        this.agentEnvironment = environment;
    }

    @Override
    protected String getToProcess(BootEnvironment bootEnvironment) {
        return "java";
    }

    protected List<String> getJavaOpts() throws Exception {
        log.debug("append base boot args ...");

        List<String> lines = new ArrayList<>();
        // 设置缺省的 javaOpts 参数
        String javaOpts = StringUtils.isNoneBlank(this.getAntActionParam().getJavaOpts()) ? this.getAntActionParam().getJavaOpts()
                : getAppEnvironment().getOrDefault(JAVA_BOOT_OPTS_KEY, DEFAULT_JAVA_BOOT_OPTS).toString();

        log.debug("JavaOpts={}", javaOpts);

        for (String item : javaOpts.split(" ")) {
            if (StringUtils.isNoneBlank(item)) {
                lines.add(getAppEnvironment().replacePlaceholder(item.trim()));
            }
        }

        //禁用追加JVM参数
        AntActionLabel antActionLabel = this.getAntActionParam().getBootParam().getActionLabelByCode(SkynetConsts.ACTION_LABEL_DISABLE_APPEND_JVM_OPTIONS);
        log.debug("appendJvmOptionsProperties={}", appendJvmOptionsProperties);
        if (appendJvmOptionsProperties != null && appendJvmOptionsProperties.size() > 0 && (antActionLabel == null || !antActionLabel.getValue())) {
            appendJvmOptionsProperties.forEach(p -> {
                if (org.springframework.util.StringUtils.hasText(p)) {
                    String value = agentEnvironment.getProperty(p, "");
                    if (org.springframework.util.StringUtils.hasText(value)) {
                        log.debug("append -D{}={}", p, value);
                        lines.add(String.format("-D%s=%s", p, value));
                    }
                }
            });
        }

        // 添加所有-D的参数
        List<String> args = super.getWorkArgs();
        for (String arg : args) {
            if (arg.startsWith("-D")) {
                lines.add(getAppEnvironment().replacePlaceholder(arg.trim()));
            }
        }
        return lines;
    }

    @Override
    protected List<String> getMain() throws Exception {

        List<String> objList = new ArrayList<>(0);
        BootParam bootParam = this.getAntActionParam().getBootParam();
        if (bootParam == null || StringUtils.isBlank(bootParam.getMainJar())) {
            return objList;
        }

        if (bootParam.getMainJar().endsWith(".jar")) {
            objList.addAll(getJavaOpts());
            objList.add("-jar");
        }
        objList.add(bootParam.getMainJar());
        return objList;
    }
}
