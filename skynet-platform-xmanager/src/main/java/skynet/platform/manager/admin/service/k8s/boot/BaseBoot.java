package skynet.platform.manager.admin.service.k8s.boot;

import lombok.extern.slf4j.Slf4j;
import org.codehaus.plexus.util.cli.Arg;
import org.codehaus.plexus.util.cli.Commandline;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import skynet.boot.SkynetProperties;
import skynet.platform.common.domain.AntActionParam;
import skynet.platform.common.domain.BootParam;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.env.BootEnvironmentBuildParam;
import skynet.platform.common.env.BootEnvironmentBuilder;
import skynet.platform.feign.service.V3K8sAdapter;

import java.io.File;
import java.nio.file.Paths;
import java.util.*;
import java.util.Map.Entry;


/**
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component(BaseBoot.BEAN_NAME)
public class BaseBoot {

    public static final String BEAN_NAME = "boot.baseboot";

    private static final List<String> ENV_NAME = Collections.emptyList();//  Arrays.asList("SKYNET_HOME", "JAVA_HOME", "CLASSPATH", "PATH");
    private static final String EMPTY = "";

    private final SkynetProperties skynetProperties;

    private final BootEnvironmentBuilder bootEnvironmentBuilder;

    private BootEnvironment appEnvironment;

    private AntActionParam antActionParam;
    private int appPort;
    private BootParam bootParam;

    private String aid;

    public BaseBoot(SkynetProperties skynetProperties, BootEnvironmentBuilder bootEnvironmentBuilder) {
        this.skynetProperties = skynetProperties;
        this.bootEnvironmentBuilder = bootEnvironmentBuilder;
    }

    public void init(AntActionParam antActionParam, V3K8sAdapter.K8sDependResourceRequest k8sDependResourceRequest) throws Exception {
        log.debug("k8sDependResourceRequest={}", k8sDependResourceRequest);
        assert StringUtils.hasText(k8sDependResourceRequest.getIp());
        this.antActionParam = antActionParam;
        this.aid = k8sDependResourceRequest.getIndex() == 0 ? antActionParam.getActionPoint() : String.format("%s_%s", antActionParam.getActionPoint(), k8sDependResourceRequest.getIndex());
        this.bootParam = antActionParam.getBootParam();
        this.appPort = k8sDependResourceRequest.getPort() == 0 ? antActionParam.getPort() : k8sDependResourceRequest.getPort();

        // 构建环境
        this.appEnvironment = bootEnvironmentBuilder.build(
                new BootEnvironmentBuildParam()
                        .setActionPoint(antActionParam.getActionPoint())
                        .setActionId(aid)
                        .setMasterIp(k8sDependResourceRequest.getIp())
                        .setIpAddress(StringUtils.hasText(k8sDependResourceRequest.getNodeIp()) ? k8sDependResourceRequest.getNodeIp() : k8sDependResourceRequest.getIp())
                        .setAppPort(this.appPort)
                        .setExtPorts(k8sDependResourceRequest.getPorts())
        );
        //docker模式下都不检测IP
        this.appEnvironment.put("skynet.check-ip.enabled", "false");
        this.appEnvironment.replacePlaceholder();
    }


    /**
     * （由于工作目录中可能有暂未符）替换后的 工作目录
     */
    public final String getWorkHome() {
        return appEnvironment.getOrDefault("WORK_HOME", EMPTY).toString();
    }

    protected String getToProcess(BootEnvironment bootEnvironment) {
        return bootParam == null ? EMPTY : bootParam.getWorkCmd();
    }

    protected List<String> getMain() throws Exception {
        return new ArrayList<>(0);
    }

    public LinkedHashMap<String, String> getWorkEnvs() {
        LinkedHashMap<String, String> objMap = new LinkedHashMap<>();

        for (String env : ENV_NAME) {
            if (StringUtils.hasText(System.getenv(env))) {
                objMap.put(env, System.getenv(env));
            }
        }

        if (bootParam != null) {
            for (Entry<String, String> item : bootParam.getWorkEnvs().entrySet()) {
                objMap.put(item.getKey(), replacePlaceholderD(appEnvironment.replacePlaceholder(item.getValue()), bootParam.getWorkEnvs()));
            }
        }

        return objMap;
    }

    public List<String> getWorkArgs() throws Exception {
        List<String> objList = new ArrayList<>();
        if (bootParam != null && bootParam.getWorkArgs() != null) {
            objList.addAll(bootParam.getWorkArgs());
        }
        return objList;
    }

    public File getAppConfigFile() {
        return Paths.get(skynetProperties.getHome(), "tmp", String.format("%s.app.properties", aid)).toFile();
    }


    public final Commandline getCommand() throws Exception {
        log.debug("create  base boot command ...");

        // 创建命令行工具
        String toProcess = appEnvironment.replacePlaceholder(getToProcess(appEnvironment));

        // 替换换行符 和多余的空格
        toProcess = toProcess.replaceAll("\\s+", " ");

        Commandline commandline = StringUtils.hasText(toProcess) ? new Commandline(toProcess) : new Commandline();
        String workDir = getWorkHome();

        // 设置工作目录
        if (StringUtils.hasText(workDir)) {
            commandline.setWorkingDirectory(new File(workDir));
        }

        // 应用环境变量
        LinkedHashMap<String, String> envs = getWorkEnvs();
        for (Entry<String, String> env : envs.entrySet()) {
            commandline.addEnvironment(env.getKey(), replacePlaceholderD(appEnvironment.replacePlaceholder(env.getValue()), envs));
        }

        for (String arg : this.getMain()) {
            Arg argObject = commandline.createArg();
            argObject.setValue(appEnvironment.replacePlaceholder(arg));
        }

        // 设置参数
        List<String> args = this.getWorkArgs();
        for (String arg : args) {
            Arg argObject = commandline.createArg();
            argObject.setValue(appEnvironment.replacePlaceholder(arg));
        }

        log.debug("create  base boot command:[{}]", commandline);

        return commandline;
    }

    public final int getAppPort() {
        return appPort;
    }


    /**
     * @return the antActionParam
     */
    public AntActionParam getAntActionParam() {
        return antActionParam;
    }

    private static String replacePlaceholderD(String s, Map<String, String> vars) {
        String ret = s;
        String[] phs = org.apache.commons.lang3.StringUtils.substringsBetween(ret, "$", "/");
        if (phs != null) {
            for (String ph : phs) {
                // 替换环境变量
                String t = vars.get(ph);
                if (StringUtils.hasText(t)) {
                    ret = StringUtils.replace(ret, String.format("$%s", ph), t);
                }
            }
        }
        return ret;
    }

    /**
     * @return the bootEnvironment
     */
    public BootEnvironment getAppEnvironment() {
        return appEnvironment;
    }

    public String getAid() {
        return aid;
    }
}
