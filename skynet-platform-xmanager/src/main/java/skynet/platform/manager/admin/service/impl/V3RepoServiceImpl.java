package skynet.platform.manager.admin.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.RepoFileDto;
import skynet.platform.manager.admin.core.RepoService;
import skynet.platform.manager.admin.domain.RepoFileDo;
import skynet.platform.manager.admin.service.V3RepoService;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
@RefreshScope
@Slf4j
@Service

public class V3RepoServiceImpl implements V3RepoService {

    private final RepoService repoService;

    public V3RepoServiceImpl(RepoService repoService) {
        this.repoService = repoService;
    }

    @Override
    public void outputFile(String plugin, String filePath, boolean isArchived, OutputStream os) throws IOException {
        repoService.outputFile(plugin, filePath, isArchived, os);
    }

    @Override
    public void outputFile(String plugin, String filePath, boolean isArchived, long offset, int length, OutputStream os) throws IOException {
        repoService.outputFile(plugin, filePath, isArchived, offset, length, os);
    }

    @Override
    public void outputFile(String plugin, String filePath, ZipOutputStream zos, String zipPath) throws IOException {
        repoService.outputFile(plugin, filePath, zos, zipPath);
    }

    @Override
    public void inputFile(String plugin, String filePath, InputStream in) throws IOException {
        repoService.inputFile(plugin, filePath, in);
    }

    @Override
    public List<RepoFileDto> list(String plugin) throws IOException {
        return list(plugin, null);
    }

    @Override
    public List<RepoFileDto> list(String plugin, String regex) throws IOException {
        List<RepoFileDo> objList = repoService.list(plugin, regex);
        return JSON.parseObject(JSON.toJSONString(objList), new TypeReference<>() {
        });
    }

    @Override
    public void deleteFile(String plugin, String filePath, boolean isArchived) {
        repoService.deleteFile(plugin, filePath, isArchived);
    }

    @Override
    public void archiveFile(String plugin, String filePath) {
        repoService.archiveFile(plugin, filePath);
    }

    @Override
    public void restoreFile(String plugin, String filePath) {
        repoService.restoreFile(plugin, filePath);
    }

    @Override
    public String getRepoPath() {
        return repoService.getRepoPath();
    }

    @Override
    public RepoFileDto getFileInfo(String plugin, String filePath, boolean isArchived) throws IOException {
        RepoFileDo repoFileDo = repoService.getFileInfo(plugin, filePath, isArchived);
        return (repoFileDo != null) ? JSON.parseObject(repoFileDo.toString(), RepoFileDto.class) : null;
    }
}
