package skynet.platform.manager.admin.controller;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.ActuatorItem;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3ActionRuntime;
import skynet.platform.manager.admin.core.ActionStatusService;
import skynet.platform.manager.admin.core.ActuatorService;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Profile("!mock")
@RestController
@ExposeSwagger2

public class V3ActionRuntimeController implements V3ActionRuntime {
    private final ActionStatusService actionStatusService;
    private final ActuatorService actuatorService;

    public V3ActionRuntimeController(ActionStatusService actionStatusService, ActuatorService actuatorService) {
        this.actionStatusService = actionStatusService;
        this.actuatorService = actuatorService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<ActuatorItem>> getActuator(@Parameter(description = "服务部署IP") @RequestParam String ip,
                                                             @Parameter(description = "端口") @RequestParam int port,
                                                             @Parameter(description = "服务坐标") @RequestParam String actionPoint) {

        SkynetApiResponse<List<ActuatorItem>> response = new SkynetApiResponse<>();
        try {
            response.setData(actuatorService.getActuatorLinks(ip, port, actionPoint));
        } catch (Exception e) {
            log.error("getActuator error. ip:{}, port:{}", ip, port, e);
            response.setException(e);
        }
        log.debug("getActuator. response:{}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getNodeStatus(@Parameter(description = "服务部署IP") @RequestParam(name = "ip") String ip,
                                                   @Parameter(description = "端口") @RequestParam(name = "port") int port,
                                                   @Parameter(name = "aid") //value = "ActionId"
                                                   @RequestParam(value = "aid", required = false) String aid) {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        try {
            response.setData(actionStatusService.getNodeStatus(ip, port, aid).toJson());
        } catch (Exception e) {
            log.error("getNodeStatus error. ip={}, port={}, aid={},err={}", ip, port, aid, e.getMessage());
            response.setException(e);
        }
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getBootStatus(@Parameter(description = "服务部署IP") @RequestParam String ip,
                                                   @Parameter(description = "端口") @RequestParam int port,
                                                   @Parameter(name = "aid")  //value = "ActionId"
                                                   @RequestParam String aid) {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        try {
            response.setData(actionStatusService.getBootStatus(ip, port, aid).toJson());
        } catch (Exception e) {
            log.error("getBootStatus error. ip={}, port={}, aid={},err={}", ip, port, aid, e.getMessage());
            response.setException(e);
        }
        return response;
    }
}
