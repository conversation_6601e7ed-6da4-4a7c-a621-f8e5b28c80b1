package skynet.platform.manager.admin.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import skynet.platform.common.auth.ManagerEncryptor;


/**
 * 服务器密码加解密 RestAPI
 *
 * <AUTHOR>
 */
@Profile("!mock")
@Slf4j
@RestController

public class V3EncryptionController {
    private final static String PREFIX = "/skynet/api/v3";

    private final ManagerEncryptor managerEncryptor;

    public V3EncryptionController(ManagerEncryptor managerEncryptor) {
        this.managerEncryptor = managerEncryptor;
    }

    @PostMapping(value = PREFIX + "/decrypt", produces = {MediaType.TEXT_PLAIN_VALUE}, consumes = {MediaType.TEXT_PLAIN_VALUE})
    @PreAuthorize("hasRole('ADMIN')")
    public String decrypt(@RequestBody String base64Pwd) throws Exception {
        log.info("base64Pwd={}", base64Pwd);
        return managerEncryptor.decrypt(base64Pwd);
    }

    @PostMapping(value = PREFIX + "/encrypt", produces = {MediaType.TEXT_PLAIN_VALUE}, consumes = {MediaType.TEXT_PLAIN_VALUE})
    @PreAuthorize("hasRole('ADMIN')")
    public String encrypt(@RequestBody String pwd) throws Exception {
        log.info("pwd={}", pwd);
        return managerEncryptor.encrypt(pwd);
    }
}
