package skynet.platform.manager.admin.controller;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.DiagnosisGroup;
import skynet.platform.feign.model.DiagnosisResponse;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3Diagnosis;
import skynet.platform.manager.admin.service.V3DiagnosisService;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@ExposeSwagger2

public class V3DiagnosisController implements V3Diagnosis {

    private final V3DiagnosisService v3DiagnosisService;

    public V3DiagnosisController(V3DiagnosisService v3DiagnosisService) {
        this.v3DiagnosisService = v3DiagnosisService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<String>> getGroups() throws Exception {
        log.debug("getGroups");
        List<DiagnosisGroup> groupList = v3DiagnosisService.getItems();
        return SkynetApiResponse.success(groupList.stream().map(DiagnosisGroup::getTitle).toList());
    }


    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<DiagnosisResponse>> getReport(
            @Parameter(description = "服务器IP") @PathVariable String ip,
            @PathVariable @Parameter(description = "分组序号") int groupIndex) throws Exception {
        log.debug("getReport ip = {},index = {}", ip, groupIndex);
        List<DiagnosisResponse> objList = v3DiagnosisService.getReport(ip, groupIndex);
        return SkynetApiResponse.success(objList);
    }
}
