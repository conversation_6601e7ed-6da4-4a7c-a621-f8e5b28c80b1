package skynet.platform.manager.admin.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.TypeReference;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.repository.config.setting.block.ConfigBlockService;
import skynet.platform.common.repository.config.setting.block.data.ConfigBlock;
import skynet.platform.feign.model.ConfigBlockDto;
import skynet.platform.feign.model.NoDataResponse;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3ConfigBlock;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 配置块管理
 *
 * <AUTHOR> 2023年09月28日
 * @since 3.4.10
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2

public class V3ConfigBlockController implements V3ConfigBlock {

    private final ConfigBlockService configBlockService;

    public V3ConfigBlockController(ConfigBlockService configBlockService) {
        this.configBlockService = configBlockService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<ConfigBlockDto>> list() throws IOException {
        log.debug("fetchAll ConfigBlock");
        SkynetApiResponse<List<ConfigBlockDto>> response = new SkynetApiResponse<>();
        try {
            List<ConfigBlock> configBlockList = configBlockService.fetchAll();
            List<ConfigBlockDto> dtoList = new ArrayList<>(configBlockList.size());
            for (ConfigBlock item : configBlockList) {
                ConfigBlockDto dto = new ConfigBlockDto();
                BeanUtils.copyProperties(item, dto);
                dtoList.add(dto);
            }
            response.setData(dtoList);
        } catch (Exception e) {
            log.error("fetchAll ConfigBlock error", e);
            response.setException(e);
        }
        log.debug("fetchAll response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "配置管理", operation = "排序配置", message = "Detail=#{#codes}")
    @PreAuthorize("hasRole('ADMIN')")
    public NoDataResponse sort(List<String> codes) {
        log.info("sort codes={}", codes);
        Assert.notEmpty(codes, "codes is empty");
        NoDataResponse response = new NoDataResponse();
        try {
            List<ConfigBlock> configBlockList = configBlockService.fetchAll();

            Map<String, Integer> codeToIndexMap = IntStream.range(0, codes.size())
                    .boxed()
                    .collect(Collectors.toMap(codes::get, i -> i * 10));

            for (ConfigBlock configBlock : configBlockList) {
                String code = configBlock.getCode();
                if (codeToIndexMap.containsKey(code)) {
                    configBlock.setIndex(codeToIndexMap.get(code));
                    configBlockService.update(configBlock);
                }
            }
        } catch (Exception e) {
            log.error("sort error", e);
            response.setException(e);
        }
        log.debug("sort response={}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<ConfigBlockDto> fetch(String code) throws IOException {
        log.debug("fetch code={}", code);
        SkynetApiResponse<ConfigBlockDto> response = new SkynetApiResponse<>();
        try {
            ConfigBlock configBlock = configBlockService.fetch(code);
            if (configBlock != null) {
                ConfigBlockDto dto = new ConfigBlockDto();
                BeanUtils.copyProperties(configBlock, dto);
                response.setData(dto);
            } else {
                response.setCode(-1);
                response.setMessage("config block does not exist");
            }
        } catch (Exception e) {
            log.error("fetch code=" + code, e);
            response.setException(e);
        }
        log.debug("fetch response={}", response);
        return response;
    }


    @Override
    @AuditLog(module = "配置管理", operation = "更新配置", message = "Detail=#{#configBlockDto}")
    @PreAuthorize("hasRole('ADMIN')")
    public NoDataResponse update(ConfigBlockDto configBlockDto) {
        log.debug("update configBlockDto={}", configBlockDto);
        NoDataResponse response = new NoDataResponse();
        try {
            ConfigBlock configBlock = new ConfigBlock();
            BeanUtils.copyProperties(configBlockDto, configBlock);
            configBlockService.update(configBlock);
        } catch (Exception e) {
            log.error("update error", e);
            response.setException(e);
        }
        log.debug("update response={}", response);
        return response;
    }


    @Override
    @AuditLog(module = "配置管理", operation = "删除配置", message = "Detail=#{#codes}")
    @PreAuthorize("hasRole('ADMIN')")
    public NoDataResponse deletes(@RequestParam String[] codes) {
        log.debug("delete codes={}", (Object) codes);
        NoDataResponse response = new NoDataResponse();
        try {
            for (String code : codes) {
                configBlockService.delete(code);
            }
        } catch (Exception e) {
            log.error("delete error", e);
            response.setException(e);
        }
        log.debug("deletes response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "配置管理", operation = "删除配置", message = "Detail=#{#code}")
    @PreAuthorize("hasRole('ADMIN')")
    public NoDataResponse delete(String code) {
        log.debug("delete code={}", code);
        NoDataResponse response = new NoDataResponse();
        try {
            configBlockService.delete(code);
        } catch (Exception e) {
            log.error("delete error", e);
            response.setException(e);
        }
        log.debug("delete response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "配置管理", operation = "导入配置")
    @PreAuthorize("hasRole('ADMIN')")
    public NoDataResponse importBlock(@RequestPart("file") MultipartFile file) {
        log.debug("import config begin...");
        NoDataResponse response = new NoDataResponse();
        try {
            List<String> configLine = IOUtils.readLines(file.getInputStream(), StandardCharsets.UTF_8);
            StringBuilder stringBuilder = new StringBuilder();
            for (String line : configLine) {
                stringBuilder.append(line);
            }
            log.debug("config text = {} ", stringBuilder);
            List<ConfigBlock> objList = JSON.parseObject(stringBuilder.toString(), new TypeReference<List<ConfigBlock>>() {
            });
            for (ConfigBlock configBlock : objList) {
                log.debug("update code={}", configBlock.getCode());
                configBlockService.update(configBlock);
            }
        } catch (Exception e) {
            log.error("fail to import config: ", e);
            response.setException(e);
        }
        log.trace("import config finish");
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public ResponseEntity<StreamingResponseBody> exportBlock(@RequestParam String[] codes) {
        log.debug("export config begin...");
        if (codes == null || codes.length == 0) {
            return ResponseEntity.badRequest().build();
        }
        List<ConfigBlock> objList = new ArrayList<>(codes.length);

        for (String code : codes) {
            ConfigBlock configBlock = configBlockService.fetch(code);
            if (configBlock != null) {
                objList.add(configBlock);
            }
        }
        StreamingResponseBody body = outputStream -> {
            outputStream.write(JSON.toJSONString(objList, JSONWriter.Feature.PrettyFormat).getBytes(StandardCharsets.UTF_8));
        };
        String filename = String.format("skynet.config.%s", (codes.length > 1) ? "export" : codes[0]);
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
        String currentTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        headers.add(HttpHeaders.CONTENT_DISPOSITION, String.format("attachment; filename=\"%s-%s.json\"", currentTime, filename));
        ResponseEntity<StreamingResponseBody> ret = new ResponseEntity<>(body, headers, HttpStatus.OK);
        return ret;
    }
}
