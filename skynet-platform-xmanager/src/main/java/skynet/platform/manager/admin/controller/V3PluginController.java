package skynet.platform.manager.admin.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.PluginDto;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3Plugin;
import skynet.platform.manager.admin.domain.DataTypeConverter;
import skynet.platform.manager.admin.service.V3ActionDeployService;
import skynet.platform.manager.admin.service.V3PluginService;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.Comparator;
import java.util.List;

/**
 * plugin
 *
 * <AUTHOR>
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2

public class V3PluginController implements V3Plugin {
    private static final ObjectMapper MAPPER = new ObjectMapper();

    private final IAntConfigService antConfigService;
    private final V3PluginService v3PluginService;
    private final V3ActionDeployService v3ActionDeployService;

    public V3PluginController(IAntConfigService antConfigService, V3PluginService v3PluginService,
                              V3ActionDeployService v3ActionDeployService) {
        this.antConfigService = antConfigService;
        this.v3PluginService = v3PluginService;
        this.v3ActionDeployService = v3ActionDeployService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<PluginDto>> getAllPlugins() {
        log.debug("getAllPlugins enter");

        SkynetApiResponse<List<PluginDto>> resp;
        try {
            List<NodeDescription> nodeList = antConfigService.getPlugins();
            if (log.isDebugEnabled()) {
                log.debug("nodeList={}", MAPPER.writeValueAsString(nodeList));
            }
            List<PluginDto> pluginDtoList = DataTypeConverter.batchToPluginDto(nodeList);
            // 返回结果按照 index 值排序
            pluginDtoList.sort(Comparator.comparing(PluginDto::getIndex));

            resp = SkynetApiResponse.success(pluginDtoList);
        } catch (Exception e) {
            log.error("getAllPlugins exception", e);
            resp = SkynetApiResponse.fail(e);
        }

        return resp;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<PluginDto> getPlugin(@Parameter(description = "系统编码") @PathVariable String pluginCode) {
        log.debug("getPlugin pluginCode={}", pluginCode);

        SkynetApiResponse<PluginDto> resp;

        try {
            NodeDescription nd = antConfigService.getPlugin(pluginCode);
            if (nd == null) {
                resp = SkynetApiResponse.fail(new ApiRequestException(ApiRequestErrorCode.PLUGIN_NOT_EXIST));
                return resp;
            }
            PluginDto pd = DataTypeConverter.toPluginDto(nd);
            resp = SkynetApiResponse.success(pd);
            log.debug("pd={}", pd);
        } catch (Exception e) {
            log.error("getPlugin exception", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("getPlugin response={}", resp);
        return resp;
    }

    @Override
    @AuditLog(module = "应用系统管理", operation = "新增应用系统", message = "param=#{#pluginDto}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> createPlugin(@RequestBody PluginDto pluginDto) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("pluginDto={}", MAPPER.writeValueAsString(pluginDto));
        }

        SkynetApiResponse<Void> resp;
        try {
            v3PluginService.createPlugin(pluginDto);
            resp = SkynetApiResponse.success();
        } catch (Exception e) {
            log.error("save plugin exception", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("createPlugin response={}", resp);
        return resp;
    }

    @Override
    @AuditLog(module = "应用系统管理", operation = "移除应用系统", message = "pluginCode=#{#pluginCode}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> deletePlugin(@Parameter(description = "系统编码") @PathVariable String pluginCode) {
        log.debug("deletePlugin pluginCode={}", pluginCode);

        SkynetApiResponse<Void> resp;

        try {
            v3PluginService.checkPlugin(pluginCode);

            String path = String.format("%s/%s", antConfigService.getSkynetPluginPath(), pluginCode);
            antConfigService.delData(path);

            // 删除已经分配的所有服务。
            v3ActionDeployService.removeDeployPlugin(pluginCode);
            resp = SkynetApiResponse.success();
        } catch (Exception e) {
            log.error("deletePlugin exception", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("deletePlugin response={}", resp);

        return resp;
    }

    /**
     * 更新服务定义 本质上和创建服务定义一样
     *
     * @param pluginCode plugin code
     * @param pluginDto  plugin dto
     * @return void
     * @throws Exception exception
     */
    @Override
    @AuditLog(module = "应用系统管理", operation = "更新应用系统", message = "pluginCode=#{#pluginCode}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> updatePlugin(@Parameter(description = "系统编码") @PathVariable String pluginCode,
                                                @RequestBody PluginDto pluginDto) throws Exception {
        log.debug("pluginCode={}", pluginDto);

        if (log.isDebugEnabled()) {
            log.debug("pluginDto={}", MAPPER.writeValueAsString(pluginDto));
        }

        SkynetApiResponse<Void> resp;
        try {
            v3PluginService.checkPlugin(pluginCode);

            v3PluginService.savePlugin(pluginDto);
            resp = SkynetApiResponse.success();
        } catch (Exception e) {
            log.error("update plugin exception", e);
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("update response={}", resp);
        return resp;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getProperties(@Parameter(description = "系统编码") @PathVariable String pluginCode) {
        SkynetApiResponse<String> resp;
        try {
            String data = this.v3PluginService.getProperties(pluginCode);
            resp = SkynetApiResponse.success(data);
        } catch (Exception e) {
            resp = SkynetApiResponse.fail(e);
        }
        log.debug("getProperties response={}", resp);

        return resp;
    }

    @Override
    @AuditLog(module = "应用系统管理", operation = "更新应用系统", message = "pluginCode=#{#pluginCode},param=#{#body}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> updateProperties(@Parameter(description = "系统编码") @PathVariable String pluginCode,
                                                    @RequestBody(required = false) String body) {
        SkynetApiResponse<Void> ret = SkynetApiResponse.success();
        try {
            this.v3PluginService.updateProperties(pluginCode, body);
        } catch (Exception e) {
            ret = SkynetApiResponse.fail(e);
        }
        log.debug("updateProperties response={}", ret);

        return ret;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getLoggingLevels(@Parameter(description = "系统编码") @PathVariable String pluginCode) {
        SkynetApiResponse<String> ret;
        try {
            String data = this.v3PluginService.getLoggingLevels(pluginCode);
            ret = SkynetApiResponse.success(data);
        } catch (Exception e) {
            ret = SkynetApiResponse.fail(e);
        }
        log.debug("getLoggingLevels response={}", ret);

        return ret;
    }

    @Override
    @AuditLog(module = "应用系统管理", operation = "更新应用系统日志级别配置", message = "pluginCode=#{#pluginCode},param=#{#body}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> updateLoggingLevels(@Parameter(description = "系统编码") @PathVariable String pluginCode,
                                                       @RequestBody(required = false) String body) {
        SkynetApiResponse<Void> ret = SkynetApiResponse.success();
        try {
            this.v3PluginService.updateLoggingLevels(pluginCode, body);
        } catch (Exception e) {
            ret = SkynetApiResponse.fail(e);
        }
        log.debug("updateLoggingLevels response={}", ret);
        return ret;
    }
}
