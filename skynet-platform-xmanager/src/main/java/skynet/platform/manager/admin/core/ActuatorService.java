package skynet.platform.manager.admin.core;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import skynet.boot.security.client.AuthRestTemplateBuilder;
import skynet.boot.security.config.SkynetAuthClientProperties;
import skynet.platform.common.domain.AntActionParam;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.AntActionLabel;
import skynet.platform.feign.model.ActuatorItem;
import skynet.platform.manager.admin.domain.ActuatorMapping;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2020/11/6 9:09
 */
@Slf4j
@Service
public class ActuatorService {

    private final Cache<String, List<ActuatorItem>> actuatorItemListCache;

    @Value("${skynet.boot.host.actuator.dispatcher-servlet-key:dispatcherServlet}")
    private String dispatcherServletKey;

    private static final Pattern ACTUATOR_PATTERN = Pattern.compile("'([^']+)'");
    private final RestTemplate defaultAuthRestTemplate;
    private final IAntConfigService antConfigService;
    private final AuthRestTemplateBuilder authRestTemplateBuilder;
    private final Map<String, RestTemplate> restTemplateMap = new HashMap<>();

    public ActuatorService(@Qualifier("authRestTemplate") RestTemplate authRestTemplate, IAntConfigService antConfigService, AuthRestTemplateBuilder authRestTemplateBuilder) {
        this.defaultAuthRestTemplate = authRestTemplate;
        this.antConfigService = antConfigService;
        this.authRestTemplateBuilder = authRestTemplateBuilder;
        this.actuatorItemListCache = CacheBuilder.newBuilder().maximumSize(100).expireAfterAccess(120, TimeUnit.SECONDS).concurrencyLevel(5).recordStats().build();
    }

    public List<ActuatorItem> getActuatorLinks(String ip, int port, String actionPoint) throws Exception {
        Assert.hasText(ip, "The ip is Blank.");
        log.debug("The target\t{}:{};actionPoint={}", ip, port, actionPoint);
        String key = String.format("%s:%d_%s", ip, port, actionPoint);

        List<ActuatorItem> objList = actuatorItemListCache.getIfPresent(key);
        if (objList == null) {
            objList = fetchActuatorLinks(ip, port, actionPoint);
            actuatorItemListCache.put(key, objList);
        }
        return objList;
    }

    private List<ActuatorItem> fetchActuatorLinks(String ip, int port, String actionPoint) throws Exception {
        List<ActuatorItem> objList = new ArrayList<>(0);
        String protocol = "http";
        String contextPath = "/actuator";
        String authInfo = "default";

        if (StringUtils.isNoneBlank(actionPoint)) {
            ActionNameContract actionNameContract = new ActionNameContract(actionPoint);
            AntActionParam antActionParam = this.antConfigService.getActionParam(actionNameContract.getPluginCode(), actionNameContract.getActionCode());
            if (antActionParam == null || antActionParam.getBootParam() == null
                    || StringUtils.isBlank(antActionParam.getBootParam().getActionProtocol())
                    || !antActionParam.getBootParam().getActionProtocol().toLowerCase().startsWith("http")) {
                return objList;
            }

            //注入 Metric 监测的端口配置（让Mesh添加到转发路由中）
            AntActionLabel enableActuatorLabel = antActionParam.getBootParam().getActionLabelByCode("enableActuator");
            if (enableActuatorLabel == null || !enableActuatorLabel.getValue()) {
                return objList;
            }
            protocol = antActionParam.getBootParam().getActionProtocol();
            if (org.springframework.util.StringUtils.hasText(enableActuatorLabel.getExtProperty())) {
                //[base64(user:pwd)@]/actuator
                String[] extProperty = enableActuatorLabel.getExtProperty().trim().split("@");
                if (extProperty.length == 2) {
                    authInfo = extProperty[0].trim();
                    contextPath = extProperty[1].trim();
                } else {
                    contextPath = extProperty[0].trim();
                }
            }
        }

        String urlRoot = String.format("%s://%s:%d", protocol, ip, port);

        String actuatorUrlRoot = String.format("%s%s", urlRoot, contextPath);
        RestTemplate restTemplate = getRestTemplate(actionPoint, authInfo);
        String json = httpRequestJson(actuatorUrlRoot, restTemplate);
        objList = parseActuator4SpringBoot2(json, urlRoot);

        log.debug("the ActuatorItem items size:{}", objList.size());
        //排序
        return objList.stream().sorted(Comparator.comparing(ActuatorItem::getRel)).toList();
    }

    private RestTemplate getRestTemplate(String actionPoint, String base64IAuthInfo) {
        if (!restTemplateMap.containsKey(base64IAuthInfo)) {
            try {
                String authInfo = new String(Base64.getDecoder().decode(base64IAuthInfo), StandardCharsets.UTF_8);
                String[] authInfos = authInfo.split(":");
                SkynetAuthClientProperties properties = new SkynetAuthClientProperties().setUser(authInfos[0]).setPassword(authInfos[1]);
                RestTemplate restTemplate = authRestTemplateBuilder.build(properties);
                restTemplateMap.put(base64IAuthInfo, restTemplate);
            } catch (Exception e) {
                log.error("The actionPoint={} config Error. base64IAuthInfo={} [Error={}].", actionPoint, base64IAuthInfo, e.getMessage());
            }
        }
        return restTemplateMap.getOrDefault(base64IAuthInfo, defaultAuthRestTemplate);
    }


    private String httpRequestJson(String actuatorUrlRoot, RestTemplate restTemplate) {

        String url = String.format("%s/mappings", actuatorUrlRoot);
        log.debug("The actuator target url = \t{}", url);
        try {
            return restTemplate.getForObject(url, String.class);
        } catch (HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                System.out.printf("Request error. Url = %s; Err = %s%n", url, e.getMessage());
                log.warn("Request error. Url = {}; Err = {}", url, e.getMessage());
            }
        } catch (Exception e) {
            log.error("Request error. Url = {}; Err = {}", url, e.getMessage());
        }
        return null;
    }


    private List<ActuatorItem> parseActuator4SpringBoot2(String json, String actuatorUrlRoot) {
        List<ActuatorItem> objList = new ArrayList<>();
        if (StringUtils.isBlank(json)) {
            return objList;
        }
        if (json.trim().startsWith("{")) {
            log.debug("parse mappings...");
            JSONObject jsonObject = JSON.parseObject(json);
            JSONArray jsonArray = new JSONArray();
            findDispatcherServletList(jsonObject, jsonArray);
            List<ActuatorMapping> mappings = jsonArray.toList(ActuatorMapping.class);
            for (ActuatorMapping mapping : mappings) {
                if (mapping.getHandler().startsWith("Actuator web endpoint")) {
                    Matcher matcher = ACTUATOR_PATTERN.matcher(mapping.getHandler());
                    if (matcher.find()) {
                        String rel = matcher.group(1);
                        ActuatorMapping.RequestMappingConditions requestMappingConditions = mapping.getDetails().getRequestMappingConditions();
                        String path = !requestMappingConditions.getPatterns().isEmpty() ? requestMappingConditions.getPatterns().getFirst() : "/notfound";
                        String method = !requestMappingConditions.getMethods().isEmpty() ? requestMappingConditions.getMethods().getFirst() : "GET";
                        String href = String.format("%s%s", actuatorUrlRoot, path);
                        objList.add(new ActuatorItem(rel, href, method));
                    }
                }
            }
            log.debug("parse mappings size={}.", objList.size());
        }
        return objList;
    }


    private void findDispatcherServletList(JSONObject jsonObject, JSONArray jsonArray) {
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);
            if (dispatcherServletKey.equalsIgnoreCase(key) && jsonObject.get(key) instanceof JSONArray) {
                jsonArray.addAll(jsonObject.getJSONArray(key));
            }
            if (value instanceof JSONObject) {
                findDispatcherServletList((JSONObject) value, jsonArray);
            }
        }
    }


}
