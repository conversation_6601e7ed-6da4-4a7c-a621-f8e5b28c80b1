package skynet.platform.manager.admin.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.kubernetes.client.util.KubeConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import skynet.boot.exception.SkynetException;
import skynet.boot.security.SkynetEncryption;
import skynet.platform.common.auth.ManagerEncryptor;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.domain.ServerLoginParam;
import skynet.platform.common.domain.SkyosCluster;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.utils.SkyosClusterUtils;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.*;
import skynet.platform.manager.config.ManagerProperties;

import java.io.StringReader;
import java.net.URI;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.*;

/**
 * 节点管理
 */
@Slf4j
public abstract class V3BaseAgentService implements AutoCloseable {

    private final static Map<String, Runnable> AGENT_DEPLOY_THREAD_MAP = new ConcurrentHashMap<>();
    private final Cache<String, Optional<AgentVersionDto>> agentVersionDtoCache;

    protected final ManagerEncryptor managerEncryptor;
    protected final SkynetEncryption skynetEncryption;
    protected final IAntConfigService antConfigService;
    protected final ManagerProperties managerProperties;
    private final ExecutorService executorService;

    public V3BaseAgentService(ManagerEncryptor managerEncryptor, SkynetEncryption skynetEncryption,
                              IAntConfigService antConfigService, ManagerProperties managerProperties) {
        this.managerEncryptor = managerEncryptor;
        this.skynetEncryption = skynetEncryption;
        this.antConfigService = antConfigService;
        this.managerProperties = managerProperties;

        this.agentVersionDtoCache = CacheBuilder.newBuilder()
                .maximumSize(100)
                .expireAfterAccess(5, TimeUnit.SECONDS)
                .concurrencyLevel(5)
                .recordStats()
                .build();

        int concurrency = 10;
        int capacity = 1024;
        long keepAliveTime = 0L;
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("i.t.p-%d").build();
        this.executorService = new ThreadPoolExecutor(
                concurrency, concurrency, keepAliveTime, TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(capacity),
                threadFactory,
                new ThreadPoolExecutor.AbortPolicy()
        );
    }

    /**
     * 获取节点详情
     */
    public AgentDto getAgent(AntServerParam antServerParam, Map<String, AgentVersionDto> onlineAgentVersionMap) {
        AgentDto agentDto = buildAgentDto(antServerParam);
        agentDto.setServerInfo(getServerInfo(antServerParam));
        agentDto.setStatus(getAgentStatus(agentDto, onlineAgentVersionMap));
        agentDto.setConfigBlockCodes(antServerParam.getConfigBlockCodes());

        //超时处理，避免 ssh 请求超时（目标服务器不存在），而丢失 AgentDto by lyhu 2023年09月26日13:58:10
        CountDownLatch countDownLatch = new CountDownLatch(1);
        try {
            new Thread(() -> {
                agentDto.setVersion(getAgentVersion(agentDto, onlineAgentVersionMap));
                countDownLatch.countDown();
            }).start();
            countDownLatch.await(Math.max(managerProperties.getTimeout() - 1, 1), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("getAgentVersion error={}", e.getMessage());
        }
        return agentDto;
    }

    /**
     * 新增节点
     */
    public void create(AgentDto dto) throws Exception {
        this.update(dto.getIp(), dto);
    }

    /**
     * 编辑节点
     */
    public void update(String ip, AgentDto dto) throws Exception {
        dto.setIp(ip);
        checkAgentDto(dto);
        ServerLoginParam serverLoginParam = formatServerLoginParam(dto);
        this.testAgentConnection(serverLoginParam);
        this.enrollAgent(serverLoginParam, dto.getServerTags());
        if (dto.isAutoInstall()) {
            this.deployAgent(dto.getIp(), dto.isDockerEnabled(), false);
        }
    }

    /**
     * 编辑节点配置块信息
     */
    public void update(String ip, AgentConfigBlocksDto dto) throws Exception {
        if (StringUtils.isBlank(ip)) {
            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
        }
        dto.setIp(ip);
        this.enrollAgentConfigBlocks(dto);
    }

    /**
     * 删除节点
     */
    public void delete(AntServerParam antServerParam, boolean withStopAction) throws Exception {
        try {
            this.stopAgent(antServerParam, withStopAction);
        } catch (Throwable e) {
            log.error("Stop agent err={}", e.getMessage());
        }
        this.antConfigService.delServerParam(antServerParam.getIp());
    }

    /**
     * 测试节点是否可以正常连接
     */
    public void testAgentConnection(AgentDto dto) throws Exception {
        checkAgentDto(dto);
        ServerLoginParam serverLoginParam = formatServerLoginParam(dto);
        this.testAgentConnection(serverLoginParam);
    }

    /**
     * 将 agent 部署到某个服务器
     */
    public String deployAgent(String ip, boolean dockerEnabled, boolean isForce) throws Exception {

        Assert.hasText(ip, "ip shouldn't be blank");

        if (isAgentDeploying(ip)) {
            return String.format("%s the server is being installed and cannot be distributed repeatedly", ip);
        }

        Runnable runnable = () -> {
            MDC.put("targetIP", ip);
            log.info("register server begin: ip={}, dockerEnabled={}, isForce={}", ip, dockerEnabled, isForce);
            try {
                doDeployAgent(ip, dockerEnabled, isForce);
            } catch (Exception e) {
                log.error("Deploy error", e);
            } finally {
                AGENT_DEPLOY_THREAD_MAP.remove(ip);
                log.info("remove key={}, the rest of AGENT_DEPLOY_THREAD_MAP's size is {}", ip, AGENT_DEPLOY_THREAD_MAP.size());
            }
        };

        log.info("[install] server ip={}", ip);

        // 缓存正在工作的安装线程
        AGENT_DEPLOY_THREAD_MAP.put(ip, runnable);

        executorService.submit(runnable);
        return "operation succeeded. Installing";
    }

    /**
     * 判读 agent 是否正在部署中
     */
    public boolean isAgentDeploying(String ip) {
        return AGENT_DEPLOY_THREAD_MAP.containsKey(ip);
    }

    public abstract void startAgent(AntServerParam antServerParam) throws Exception;

    public abstract void stopAgent(AntServerParam antServerParam, boolean stopAction) throws Exception;

    protected abstract void checkAgentDto(AgentDto agentDto);

    protected abstract void testAgentConnection(ServerLoginParam serverLoginParam);

    protected abstract AntServerParam formatAntServerParam(AntServerParam antServerParam, ServerLoginParam serverLoginParam) throws Exception;

    protected abstract AgentVersionDto getAgentVersion(AgentDto agentDto);

    protected abstract Map<String, Object> getServerInfo(AntServerParam server);

    protected abstract void doDeployAgent(String ip, boolean dockerEnabled, boolean isForce) throws Exception;

    /**
     * 加速卡类型，acceleratorType支持 cpu、npu、gpu
     */
    protected abstract void installMonitoring(AntServerParam antServerParam) throws Exception;

    /**
     * 安装调度组件
     */
    protected abstract void installScheduler(AntServerParam antServerParam) throws Exception;

    /**
     * 将 AgentDto 转换为 ServerLoginParam。注意，dto 中的密码是经过加密的，需要解密。
     */
    private ServerLoginParam formatServerLoginParam(AgentDto agentDto) throws Exception {

        // 对加密字段进行解密
        if (StringUtils.isNoneBlank(agentDto.getSshPassword())) {
            agentDto.setSshPassword(skynetEncryption.decrypt(agentDto.getSshPassword()));
        }
        if (StringUtils.isNoneBlank(agentDto.getRegistryPassword())) {
            agentDto.setRegistryPassword(skynetEncryption.decrypt(agentDto.getRegistryPassword()));
        }

        // 从 kubeconfig 中解析 ip 和 server port 字段
        if (StringUtils.isNotBlank(agentDto.getKubeConfig()) && StringUtils.isBlank(agentDto.getIp())) {
            KubeConfig kubeConfig;
            try {
                kubeConfig = KubeConfig.loadKubeConfig(new StringReader(agentDto.getKubeConfig()));
            } catch (Exception e) {
                log.error("kube config file format error: {}", e.getMessage());
                throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
            }
            try {
                //URL serverUrl = new URL(kubeConfig.getServer());
                URL serverUrl = URI.create(kubeConfig.getServer()).toURL();
                agentDto.setIp(serverUrl.getHost());
                agentDto.setServerPort(serverUrl.getPort());
            } catch (Exception e) {
                log.error("kube config server url [{}] error: {}", kubeConfig.getServer(), e.getMessage());
                throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
            }
        } else {
            // 解析skyos cluster master节点ip
            String skyosCluster = agentDto.getSkyosCluster();
            if (StringUtils.isNotBlank(skyosCluster)) {
                SkyosCluster cluster = SkyosClusterUtils.parseFromString(skyosCluster);
                SkyosClusterUtils.ValidationResult validationResult = SkyosClusterUtils.validateConfig(cluster);
                if (!validationResult.isValid()) {
                    log.error("skyos cluster config error: {}", validationResult.getErrors());
                    throw new SkynetException(ApiRequestErrorCode.PARAM_ILLEGAL.getCode(), String.join(",", validationResult.getErrors()));
                }
                agentDto.setIp(SkyosClusterUtils.getMasterIps(cluster).getFirst());
                agentDto.setServerPort(6443);
                agentDto.setAgentType(AgentType.KUBERNETES);
                agentDto.setAcceleratorType(cluster.getSpec().getAcceleratorType());
            }
        }

        return getServerLoginParam(agentDto);
    }

    private static @NotNull ServerLoginParam getServerLoginParam(AgentDto agentDto) {
        ServerLoginParam serverLoginParam = new ServerLoginParam();
        serverLoginParam.setIp(agentDto.getIp());
        serverLoginParam.setPort(agentDto.getSshPort());
        serverLoginParam.setPwd(agentDto.getSshPassword());
        serverLoginParam.setUser(agentDto.getSshUser());
        serverLoginParam.setDesc(agentDto.getDescription());
        serverLoginParam.setSkyosCluster(agentDto.getSkyosCluster());
        serverLoginParam.setDockerEnabled(agentDto.isDockerEnabled());
        serverLoginParam.setMonitoringEnabled(agentDto.isMonitoringEnabled());
        serverLoginParam.setAcceleratorType(agentDto.getAcceleratorType());
        serverLoginParam.setSchedulerEnabled(agentDto.isSchedulerEnabled());
        serverLoginParam.setKubeConfig(agentDto.getKubeConfig());
        serverLoginParam.setRegistryUrl(agentDto.getRegistryUrl());
        serverLoginParam.setRegistryContextPath(agentDto.getRegistryContextPath());
        serverLoginParam.setRegistryUsername(agentDto.getRegistryUsername());
        serverLoginParam.setRegistryPassword(agentDto.getRegistryPassword());
        return serverLoginParam;
    }

    /**
     * 将节点信息保存到 ZK 中
     */
    private void enrollAgent(ServerLoginParam serverLoginParam, List<String> tags) throws Exception {
        log.debug("enroll server begin: ip={}, port={}; user={};",
                serverLoginParam.getIp(), serverLoginParam.getPort(), serverLoginParam.getUser());
        AntServerParam antServerParam = this.antConfigService.getServerParam(serverLoginParam.getIp());
        antServerParam = formatAntServerParam(antServerParam, serverLoginParam);
        if (tags != null) {
            antServerParam.setTag(tags);
        }
        log.debug("enroll [{}]; antServerParam={}", antServerParam.getIp(), antServerParam);
        this.antConfigService.setServerParam(antServerParam);
        log.debug("enroll server end.");
    }

    /**
     * 将节点信息配置块保存到 ZK 中
     */
    private void enrollAgentConfigBlocks(AgentConfigBlocksDto dto) {
        log.debug("enroll server begin: ip={}, AgentConfigBlocksDto={};",
                dto.getIp(), dto);
        AntServerParam antServerParam = this.antConfigService.getServerParam(dto.getIp());
        if (ObjectUtils.isEmpty(antServerParam)) {
            log.error("ant server param ip {} not exits", dto.getIp());
            throw new ApiRequestException(ApiRequestErrorCode.AGENT_NOT_REGISTER);
        }
        antServerParam.setConfigBlockCodes(dto.getConfigBlockCodes());
        log.debug("enroll [{}]; antServerParam={}", antServerParam.getIp(), antServerParam);
        this.antConfigService.setServerParam(antServerParam);
        log.debug("enroll server end.");
    }

    /**
     * 构建 agent 基本信息
     */
    private AgentDto buildAgentDto(AntServerParam server) {
        AgentDto agentDto = new AgentDto();
        agentDto.setIp(server.getIp());
        agentDto.setAgentType(server.getType() == null ? AgentType.SERVER : server.getType());
        agentDto.setServerPort(managerProperties.getAgentServerPort());
        ServerLoginParam loginParam = server.getSsh();
        if (loginParam != null) {
            agentDto.setSshPort(loginParam.getPort());
            agentDto.setSshUser(loginParam.getUser());
            agentDto.setSshPassword(loginParam.getPwd());
            agentDto.setDockerEnabled(loginParam.isDockerEnabled());
            agentDto.setDescription(loginParam.getDesc());
        }
        ServerLoginParam k8s = server.getK8s();
        if (k8s != null) {
            agentDto.setServerPort(managerProperties.getAgentK8sPort());
            agentDto.setKubeConfig(k8s.getKubeConfig());
            agentDto.setK8sNamespace(managerProperties.getK8sNamespace());
            agentDto.setRegistryUrl(k8s.getRegistryUrl());
            agentDto.setRegistryContextPath(managerProperties.getRegistryContextPath());
            agentDto.setSkyosCluster(k8s.getSkyosCluster());
            agentDto.setRegistryUsername(k8s.getRegistryUsername());
            agentDto.setRegistryPassword(k8s.getRegistryPassword());
            agentDto.setDescription(k8s.getDesc());

            if (StringUtils.isBlank(k8s.getKubeConfig()) && StringUtils.isNotBlank(k8s.getSkyosCluster())) {
                agentDto.setAgentType(AgentType.SKYOS);
            }
        }
        agentDto.setServerTags(server.getTag());
        agentDto.setIndex(server.getIndex());
        return agentDto;
    }

    /**
     * 获取 agent 版本
     */
    private AgentVersionDto getAgentVersion(AgentDto agentDto, Map<String, AgentVersionDto> onlineAgentVersionMap) {
        AgentVersionDto agentVersion = null;
        if (onlineAgentVersionMap.containsKey(agentDto.getIp())) {
            agentVersion = onlineAgentVersionMap.get(agentDto.getIp());
        }
        if (agentVersion == null) {
            Optional<AgentVersionDto> agentVersionDto = agentVersionDtoCache.getIfPresent(agentDto.getIp());
            if (agentVersionDto == null) {
                agentVersion = getAgentVersion(agentDto);
                agentVersionDto = agentVersion == null ? Optional.empty() : Optional.of(agentVersion);
                agentVersionDtoCache.put(agentDto.getIp(), agentVersionDto);
            } else {
                log.debug("Get AgentVersion from cache.[ip={}]", agentDto.getIp());
            }
            return agentVersionDto.orElse(null);
        }
        return agentVersion;
    }

    /**
     * 获取 agent 状态
     */
    private String getAgentStatus(AgentDto agentDto, Map<String, AgentVersionDto> onlineAgentVersionMap) {
        if (onlineAgentVersionMap.containsKey(agentDto.getIp())) {
            return AgentStatus.ONLINE.toString();
        } else {
            if (isAgentDeploying(agentDto.getIp())) {
                return AgentStatus.BOOTING.toString();
            } else {
                return AgentStatus.OFFLINE.toString();
            }
        }
    }

    @Override
    public void close() throws Exception {
        if (executorService != null) {
            executorService.shutdown();
        }
    }
}
