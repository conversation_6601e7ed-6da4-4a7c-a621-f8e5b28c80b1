package skynet.platform.manager.admin.controller;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.boot.common.concurrent.ParallelService;
import skynet.platform.common.domain.AntActionRegist;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.domain.BootAction;
import skynet.platform.common.env.BootEnvironmentBuilder;
import skynet.platform.common.exception.ActionNotExistException;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.AntNodeState;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.ActionDeploymentDto;
import skynet.platform.feign.model.ActionDeploymentUpdateDto;
import skynet.platform.feign.model.AgentDeploymentDto;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3Deployment;
import skynet.platform.manager.admin.core.ServerService;
import skynet.platform.manager.admin.domain.BootServerView;
import skynet.platform.manager.admin.domain.BootWorkerView;
import skynet.platform.manager.admin.domain.DataTypeConverter;
import skynet.platform.manager.admin.service.V3ActionDeployService;
import skynet.platform.manager.admin.service.V3ActionStatusService;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.config.ManagerProperties;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2

public class V3DeploymentController implements V3Deployment {

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private final IAntConfigService antConfigService;
    private final V3ActionDeployService v3ActionDeployService;
    private final V3ActionStatusService v3ActionStatusService;
    private final ServerService serverService;
    private final ManagerProperties managerProperties;
    private final BootEnvironmentBuilder bootEnvironmentBuilder;
    private final LoadBalancerClient loadBalancerClient;
    private final ExecutorService executor;

    public V3DeploymentController(IAntConfigService antConfigService,
                                  V3ActionDeployService v3ActionDeployService, ServerService serverService,
                                  V3ActionStatusService v3ActionStatusService, ManagerProperties managerProperties, BootEnvironmentBuilder bootEnvironmentBuilder, LoadBalancerClient loadBalancerClient) {

        this.antConfigService = antConfigService;
        this.v3ActionDeployService = v3ActionDeployService;
        this.serverService = serverService;
        this.v3ActionStatusService = v3ActionStatusService;
        this.managerProperties = managerProperties;
        this.bootEnvironmentBuilder = bootEnvironmentBuilder;
        this.loadBalancerClient = loadBalancerClient;

        AtomicInteger index = new AtomicInteger(0);
        this.executor = Executors.newFixedThreadPool(16,
                Thread.ofVirtual().name("deployment-executor-", index.getAndIncrement()).factory());
    }

    /**
     * 获取所有服务器和服务(包括在线状态）部署拓扑
     *
     * @return 返回状态
     */
    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<AgentDeploymentDto>> getClusterDeployment() throws Exception {
        // 参考 skynet.platform.manager.admin.core.ActionStatusService.getServerStatus 获取在线 action 信息
        // 参考 skynet.platform.manager.admin.v2.controller.impl.ServerCtrlControllerImpl.getServers 获取所有 action

        SkynetApiResponse<List<AgentDeploymentDto>> resp;
        try {
            long start = System.currentTimeMillis();
            List<AgentDeploymentDto> agentDeploymentDtoList = new ArrayList<>();

            List<BootServerView> allServerList = serverService.getAllServer();
            if (log.isDebugEnabled()) {
                log.debug("allServerList={}", JSON.toJSONString(allServerList));
            }
            if (allServerList == null) {
                resp = SkynetApiResponse.success(null);
                return resp;
            }

            List<BootServerView> onlineServerList = v3ActionStatusService.getOnlineServerStatus();
            if (log.isDebugEnabled()) {
                log.debug("onlineServerList={}", JSON.toJSONString(onlineServerList));
            }

            List<BootServerView> newAllServerList = copyOnlineToAll(onlineServerList, allServerList);
            if (log.isDebugEnabled()) {
                log.debug("newAllServerList={}", JSON.toJSONString(newAllServerList));
            }

            //如果为1 不采用并行框架
            if (newAllServerList.size() == 1) {
                agentDeploymentDtoList.add(toAgentDeploymentDto(newAllServerList.getFirst()));
            } else if (newAllServerList.size() > 1) {
                try (ParallelService<BootServerView, AgentDeploymentDto> parallelService = new ParallelService<>(this.executor)) {
                    parallelService.submit(newAllServerList, this::toAgentDeploymentDto);
                    agentDeploymentDtoList = parallelService.getResult(managerProperties.getTimeout(), TimeUnit.SECONDS);
                }
            }
            // 增加依次根据标签、INDEX、节点类型、IP 进行排序
            agentDeploymentDtoList = agentDeploymentDtoList.stream().sorted(Comparator.comparing(AgentDeploymentDto::getSort)
                            .thenComparing(AgentDeploymentDto::getAgentType)
                            .thenComparing(AgentDeploymentDto::getIp))
                    .toList();

            resp = SkynetApiResponse.success(agentDeploymentDtoList);
            log.debug("getClusterDeployment elapsed time {}ms", System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("getClusterDeployment ", e);
            resp = SkynetApiResponse.fail(e);
        }
        return resp;
    }

    /**
     * 把 online server 信息同步到 allServerList
     *
     * @param onlineServerList list
     * @param allServerList    list
     */
    private List<BootServerView> copyOnlineToAll(List<BootServerView> onlineServerList,
                                                 List<BootServerView> allServerList) {
        if (allServerList == null) {
            return new ArrayList<>();
        }

        if (onlineServerList == null) {
            return allServerList;
        }

        for (BootServerView onlineBsv : onlineServerList) {
            Integer i = findBootServerViewByIp(onlineBsv.getIp(), allServerList);
            if (i == null) {
                log.debug("{} isn't in allServerList", onlineBsv.getIp());
                continue;
            }
            BootServerView bsv = allServerList.get(i);
            BootServerView newBsv = copy(onlineBsv, bsv);
            allServerList.set(i, newBsv);
        }

        return allServerList;
    }

    private BootServerView copy(BootServerView src, BootServerView dst) {
        if (dst == null) {
            return new BootServerView();
        }
        if (src == null) {
            return dst;
        }
        dst.setPort(src.getPort());
        dst.setPid(src.getPid());
        dst.setPath(src.getPath());
        dst.setName(src.getName());
        dst.setUp(src.getUp());
        /*dst.setBootSysView(src.getBootSysView())
        dst.setTag(src.getTag())
        dst.setLoginParam(src.getLoginParam())*/

        for (BootWorkerView bwv : src.getWorkers()) {
            Integer index = findBootActionIndexByAid(bwv.getAid(), dst.getWorkers());
            if (index == null) {
                continue;
            }
            dst.getWorkers().get(index).setState(bwv.getState());
            dst.getWorkers().get(index).getAction().setUp(bwv.getAction().getUp());
            dst.getWorkers().get(index).getAction().setPort(bwv.getAction().getPort());
            dst.getWorkers().get(index).getAction().setBootType(bwv.getAction().getBootType());
            dst.getWorkers().get(index).getAction().setWorkloadType(bwv.getAction().getWorkloadType());
            dst.getWorkers().get(index).getAction().setReplicas(bwv.getAction().getReplicas());
        }

        return dst;
    }

    /**
     * convert BootServerView to AgentDeployDto
     *
     * @param bsv
     * @return
     */
    private AgentDeploymentDto toAgentDeploymentDto(BootServerView bsv) throws Exception {
        try {
            log.debug("toAgentDeploymentDto IP={}", bsv.getIp());
            List<ActionDeploymentDto> actionDeploymentDtoList = new ArrayList<>();
            List<BootWorkerView> workers = bsv.getWorkers();
            if (workers != null && !workers.isEmpty()) {
                if (workers.size() == 1) {
                    BootWorkerView bwv = workers.getFirst();
                    actionDeploymentDtoList.add(workerToActionDeploymentDto(bwv, bsv));
                } else {
                    try (ParallelService<BootWorkerView, ActionDeploymentDto> parallelService = new ParallelService<>(this.executor)) {
                        parallelService.submit(workers, bwv -> workerToActionDeploymentDto(bwv, bsv));
                        actionDeploymentDtoList = parallelService.getResult(managerProperties.getTimeout(), TimeUnit.SECONDS);
                    }
                }
            }
            String agentStatus = bsv.getUp().name();
            actionDeploymentDtoList = actionDeploymentDtoList.stream().sorted(Comparator.comparing(ActionDeploymentDto::getStartupOrder)).toList();
            return DataTypeConverter.toAgentDeploymentDto(bsv, agentStatus, actionDeploymentDtoList);
        } catch (Exception e) {
            log.error("Get AgentDeploymentDto Error", e);
            throw e;
        }
    }

    private ActionDeploymentDto workerToActionDeploymentDto(BootWorkerView bwv, BootServerView bsv) throws Exception {
        BootAction ba = bwv.getAction();
        log.debug("Get ActionDeploymentDto actionId={}", ba.getAid());
        NodeDescription nodeDescription = this.antConfigService.getPlugin(ba.getPlugin());
        String startTime = null, upTime = null, status = null, protocol = "http";
        if (ba.isEnable() && bsv.getPort() != 0) {
            try {
                String actionId = ba.getAid();
                log.debug("Get Node Status. actionId={}", actionId);
                AntNodeState antNodeState = bwv.getState();
                if (antNodeState != null) {
                    startTime = DateFormatUtils.format(antNodeState.getStartTime(), "yyyy-MM-dd HH:mm:ss");
                    upTime = antNodeState.getUpTime();
                    status = antNodeState.getUp().name();
                    protocol = antNodeState.getProtocol() == null ? "http" : antNodeState.getProtocol();
                } else {
                    log.warn("Get IP={};PORT={};ActionID={} NodeState is Null.", bsv.getIp(), bsv.getPort(), actionId);
                }
            } catch (Exception e) {
                log.error("getNodeStatus error", e);
            }
        }
        String homePageUrl = "";
        try {
            homePageUrl = getBootActionHomePageUrl(ba);
        } catch (ActionNotExistException e) {
            log.warn("getBootActionHomePageUrl error= {}", e.getMessage());
            ba.setName(String.format("%s ([%s] service definition does not exist, please remove.)", ba.getName(), ba.getFullName()));
        } catch (Throwable e) {
            log.error("getBootActionHomePageUrl error", e);
            throw e;
        }
        return DataTypeConverter.toActionDeploymentDto(ba, nodeDescription.getName(), protocol, homePageUrl, startTime, upTime, status);
    }

    private String getBootActionHomePageUrl(BootAction ba) throws Exception {
        return "";
//太耗时，目前移除，后续待优化 by lyhu 2025年06月16日16:10:31
//        String indexPageUrl = "";
//        AntActionParam antActionParam = this.antConfigService.getActionParam(ba.getPlugin(), ba.getCode());
//        if (antActionParam != null && antActionParam.getBootParam() != null) {
//            indexPageUrl = antActionParam.getBootParam().getIndexPageUrl();
//            if (StringUtils.isNoneBlank(indexPageUrl)) {
//                indexPageUrl = indexPageUrl.trim();
//                if (indexPageUrl.length() > 1) {
//
//                    SkynetProperties skynetProperties = new SkynetProperties(bootEnvironmentBuilder.getEnvironment());
//                    skynetProperties.setActionPoint(ba.getFullName());
//                    skynetProperties.setActionId(ba.getAid());
//                    skynetProperties.setIpAddress(ba.getIp());
//                    skynetProperties.setPort(ba.getPort());
//
//                    BootEnvironment bootEnvironment = bootEnvironmentBuilder.build(skynetProperties);
//                    indexPageUrl = bootEnvironment.replacePlaceholder(indexPageUrl.trim());
//                }
//                if (!indexPageUrl.trim().toUpperCase(Locale.getDefault()).startsWith("HTTP")) {
//                    ServiceInstance serviceInstance = loadBalancerClient.choose(antActionParam.getCode());
//                    if (serviceInstance != null) {
//                        indexPageUrl = String.format("%s%s", serviceInstance.getUri(), indexPageUrl.trim());
//                    }
//                }
//            }
//        }
//        return indexPageUrl;
    }

    private Integer findBootActionIndexByAid(String aid, List<BootWorkerView> bwvList) {
        if (aid == null || bwvList == null) {
            return null;
        }

        for (int index = 0; index < bwvList.size(); ++index) {
            if (aid.equals(bwvList.get(index).getAid())) {
                return index;
            }
        }
        return null;
    }

    private Integer findBootServerViewByIp(String ip, List<BootServerView> bsvList) {
        if (bsvList == null || ip == null) {
            return null;
        }
        for (int index = 0; index < bsvList.size(); ++index) {
            if (ip.endsWith(bsvList.get(index).getIp())) {
                return index;
            }
        }
        return null;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<AgentDeploymentDto> getDeploymentByIp(@PathVariable String ip) {
        log.debug("getDeploymentByIp enter");
        try {
            AgentDeploymentDto agentDeploymentDto = getDeployment(ip, null);
            return SkynetApiResponse.success(agentDeploymentDto);
        } catch (Exception e) {
            log.error("", e);
            return SkynetApiResponse.fail(e);
        }
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<AgentDeploymentDto> getDeploymentByActionPoint(@PathVariable String ip, @PathVariable String actionPoint) {
        log.debug("getDeploymentByActionPoint enter");
        try {
            AgentDeploymentDto agentDeploymentDto = getDeployment(ip, actionPoint);
            return SkynetApiResponse.success(agentDeploymentDto);
        } catch (Exception e) {
            log.error("", e);
            return SkynetApiResponse.fail(e);
        }
    }

    /**
     * 获取某个服务器上某个服务的状态，如果 actionPoint 为空，则返回该服务器所有服务的状态
     */
    private AgentDeploymentDto getDeployment(String ip, String actionPoint) throws Exception {

        // 遍历 zk 的 /skynet/cluster/topology 节点，获取已注册服务器
        BootServerView server = serverService.getServer(ip);
        // 遍历 zk 的 /skynet/cluster/online/action 节点，获取在线服务器
        // 并依次调用每个服务的 FETCH_SERVER_STATE 和 FETCH_WORKERS_STATE 接口，获取节点上的服务列表
        BootServerView onlineServer = v3ActionStatusService.getOnlineServerStatus(ip);

        if (server == null || onlineServer == null) {
            return null;
        }

        BootServerView bsv = copy(onlineServer, server);
        if (StringUtils.isNotBlank(actionPoint)) {
            bsv.setWorkers(bsv.getWorkers().stream().filter(a -> actionPoint.equals(a.getAction().getFullName())).toList());
        }
        return toAgentDeploymentDto(bsv);
    }

    /**
     * 新增或删除部署的 action
     *
     * @param ip
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    @AuditLog(module = "服务部署管理", operation = "编辑服务部署", message = "IP=#{#ip},request=#{#request}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> editDeployment(@PathVariable String ip,
                                                  @RequestBody List<ActionDeploymentUpdateDto> request)
            throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("Update all action by ip={}, request={}", ip, MAPPER.writeValueAsString(request));
        }

        try {
            AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
            if (antServerParam == null) {
                throw new ApiRequestException(ApiRequestErrorCode.AGENT_NOT_REGISTER);
            }
            antServerParam.getActions().clear();

            //获取所有的应用系统（插件列表）
            List<NodeDescription> plugins = this.antConfigService.getPlugins();
            AtomicInteger index = new AtomicInteger();
            plugins.forEach(x -> x.setIndex(index.getAndIncrement() * 100));

            request = request.stream().sorted(Comparator.comparing(ActionDeploymentUpdateDto::getOrder)).toList();
            for (ActionDeploymentUpdateDto adu : request) {
                ActionNameContract anc = new ActionNameContract(adu.getActionPoint());
                NodeDescription plugin = plugins.stream().filter(x -> x.getCode().equalsIgnoreCase(anc.getPluginCode())).findFirst().orElse(null);

                NodeDescription nd = this.antConfigService.getAction(anc.getPluginCode(), anc.getActionCode());
                if (nd == null) {
                    throw new ApiRequestException(ApiRequestErrorCode.ACTION_NOT_EXIST);
                }
                int num = Integer.parseInt(adu.getNum());
                if (num < 1 || num > 16) {
                    throw new ApiRequestException(ApiRequestErrorCode.ACTION_NUMBER_LIMIT);
                }
                AntActionRegist antActionRegist = new AntActionRegist();
                antActionRegist.setIndex(antServerParam.getActions().size());
                antActionRegist.setCode(anc.getActionCode());
                antActionRegist.setPlugin(anc.getPluginCode());
                antActionRegist.setName(nd.getName());
                antActionRegist.setNum(num);
                antActionRegist.setReplicas(adu.getReplicas());
                antActionRegist.setNodeSelector(adu.getNodeSelector());
                antActionRegist.setIndex(plugin == null ? 0 : plugin.getIndex() + adu.getOrder());
                antActionRegist.setEnable(adu.isEnabled());
                antServerParam.getActions().add(antActionRegist);
            }

            //按照应用系统（插件） 总体排序
            antServerParam.setActions(antServerParam.getActions().stream().sorted(Comparator.comparing(AntActionRegist::getIndex)).toList());

            this.antConfigService.setServerParam(antServerParam);
            log.info("Update all action by ip={} success", ip);
            return SkynetApiResponse.success();
        } catch (Exception e) {
            log.error("EditDeployment Error.", e);
            return SkynetApiResponse.fail(e);
        }
    }

    /**
     * 新增加部署 action
     *
     * @param ip
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    @AuditLog(module = "服务部署管理", operation = "删除服务部署", message = "IP=#{#ip},request=#{#request}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> addDeployment(@PathVariable String ip,
                                                 @RequestBody List<ActionDeploymentUpdateDto> request) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("add action deployment by ip={}, request={}", ip, MAPPER.writeValueAsString(request));
        }

        SkynetApiResponse<Void> resp;
        try {
            for (ActionDeploymentUpdateDto adu : request) {
                v3ActionDeployService.incrementDeployAction(ip, adu);
            }
            resp = SkynetApiResponse.success();
            log.info("add action deployment success");
        } catch (Exception e) {
            log.error("", e);
            resp = SkynetApiResponse.fail(e);
        }
        return resp;
    }


    @Override
    @AuditLog(module = "服务部署管理", operation = "删除服务部署", message = "IP=#{#ip},actionPoint=#{#actionPoint}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> deleteDeployment(@PathVariable String ip,
                                                    @PathVariable String actionPoint) throws Exception {
        log.debug("remove deployment by ip={}, actionPoint={}", ip, actionPoint);
        SkynetApiResponse<Void> resp;
        try {
            v3ActionDeployService.removeDeployAction(ip, actionPoint);
            resp = SkynetApiResponse.success();
        } catch (Exception e) {
            log.error("", e);
            resp = SkynetApiResponse.fail(e);
        }
        return resp;
    }
}
