package skynet.platform.manager.admin.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.AppContext;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.boot.common.utils.DateUtil;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.platform.common.AppVersionBuilder;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3Cluster;
import skynet.platform.manager.admin.service.V3ClusterService;
import skynet.platform.manager.audit.annotation.AuditLog;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2

public class V3ClusterController implements V3Cluster {

    private final V3ClusterService v3ClusterService;
    private final AppContext appContext;
    private final AppVersionBuilder appVersionBuilder;
    private final SkynetZkProperties skynetZkProperties;


    public V3ClusterController(V3ClusterService v3ClusterService, AppContext appContext,
                               AppVersionBuilder appVersionBuilder, SkynetZkProperties skynetZkProperties) {
        this.v3ClusterService = v3ClusterService;
        this.appContext = appContext;
        this.appVersionBuilder = appVersionBuilder;
        this.skynetZkProperties = skynetZkProperties;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<Map<String, Object>> getInfo() {
        log.debug("getInfo");
        SkynetApiResponse<Map<String, Object>> response = new SkynetApiResponse<>();
        try {
            Map<String, Object> status = new LinkedHashMap<>();
            status.put("app_name", appContext.getNodeName());
            status.put("app_title", appContext.getSkynetProperties().getActionTitle());
            status.put("app_start", DateFormatUtils.format(appContext.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            status.put("up_time", DateUtil.countDateTime(appContext.getStartTime(), new Date()));
            status.put("app_ip", appContext.getIpAddress());
            status.put("zk_cluster_name", skynetZkProperties.getClusterName());
            status.put("zk_server_list", skynetZkProperties.getServerList());
            status.put("skynet_home", appContext.getSkynetHome());
            status.put("app_version", appVersionBuilder.getManagerVersion());
            response.setData(status);
        } catch (Exception e) {
            log.error("getInfo", e);
            response.setException(e);
        }
        log.debug("getInfo response={}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getProperties() {
        log.debug("getProperties");
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        try {
            response.setData(v3ClusterService.getProperties());
        } catch (Exception e) {
            log.error("getProperties", e);
            response.setException(e);
        }
        log.debug("getProperties response={}", response);
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<String> getLoggingLevels() {
        log.debug("getLoggingLevels");
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        try {
            response.setData(v3ClusterService.getLoggingLevels());
        } catch (Exception e) {
            log.error("getLoggingLevels", e);
            response.setException(e);
        }
        log.debug("getLoggingLevels response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "集群信息", operation = "更新集群级属性配置", message = "loggers=#{#request}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<String> updateProperties(@RequestBody(required = false) String request) {
        log.info("updateProperties, param={}", request);
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        try {
            v3ClusterService.updateProperties(request);
            response.setData("save successfully");
        } catch (Exception e) {
            log.error("updateProperties error. param={}", request, e);
            response.setException(e);
        }
        log.debug("updateProperties response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "集群信息", operation = "更新集群级日志配置", message = "loggers=#{#request}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<String> updateLoggingLevels(@RequestBody(required = false) String request) {
        log.info("updateLoggingLevels, param={}", request);
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        try {
            v3ClusterService.updateLoggingLevels(request);
            response.setData("save successfully");
        } catch (Exception e) {
            log.error("updateLoggingLevels error. param={}", request, e);
            response.setException(e);
        }
        log.debug("updateLoggingLevels response={}", response);
        return response;
    }
}
