package skynet.platform.manager.admin.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.domain.AntServerParam;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.model.GrafanaAddress;
import skynet.platform.feign.model.MenuView;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3View;
import skynet.platform.manager.admin.core.GrafanaService;
import skynet.platform.manager.admin.core.OnlineMenuService;
import skynet.platform.manager.admin.service.V3PluginService;
import skynet.platform.manager.audit.annotation.AuditLog;
import java.util.List;

/**
 * <AUTHOR>
 */
@Profile("!mock")
@Slf4j
@ExposeSwagger2

@RestController
public class V3ViewController implements V3View {
    private static final ObjectMapper MAPPER = new ObjectMapper();
    private final RestTemplate restTemplate = new RestTemplate();

    private final IAntConfigService antConfigService;
    private final V3PluginService v3PluginService;
    private final GrafanaService grafanaService;
    private final OnlineMenuService onlineMenuService;

    public V3ViewController(IAntConfigService antConfigService, V3PluginService v3PluginService, GrafanaService grafanaService, OnlineMenuService onlineMenuService) {
        this.antConfigService = antConfigService;
        this.v3PluginService = v3PluginService;
        this.grafanaService = grafanaService;
        this.onlineMenuService = onlineMenuService;
    }

    @Override
    @AuditLog(module = "视图渲染相关接口", operation = "更新顺序", message = "type=#{#type},plugin=#{#plugin},order=#{#order}")
    @PreAuthorize("hasRole('ADMIN')")
    public SkynetApiResponse<Void> adjustOrder(@Parameter(description = "实体类型,支持 agent/plugin/action-definition")
                                               @RequestParam(name = "type") String type,
                                               @Parameter(description = "服务插件 仅在 type = action-definition 时需要传")
                                               @RequestParam(name = "plugin", required = false) String plugin,
                                               @RequestBody List<String> order) throws Exception {
        if (log.isDebugEnabled()) {
            log.debug("adjustOrder type={}, order={}", type, MAPPER.writeValueAsString(order));
        }

        SkynetApiResponse<Void> resp = SkynetApiResponse.success("set sequence successfully", null);

        try {
            switch (type) {
                case "agent" -> reorderAgent(order);
                case "plugin" -> v3PluginService.reorderPlugin(order);
                case "action-definition" -> reorderAction(plugin, order);
                default -> resp = SkynetApiResponse.fail(ApiRequestErrorCode.UNSUPPORTED_TYPE);
            }
        } catch (Exception e) {
            log.error("adjustOrder Error={}", e.getMessage(), e);
            resp = SkynetApiResponse.fail(e);
        }
        return resp;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<List<MenuView>> getMenus() {
        SkynetApiResponse<List<MenuView>> ret;
        try {
            List<MenuView> menus = this.onlineMenuService.getMenusList();
            ret = SkynetApiResponse.success(menus);
        } catch (Exception e) {
            ret = SkynetApiResponse.fail(e);
        }
        return ret;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<GrafanaAddress> getGrafanaAddr() {
        SkynetApiResponse<GrafanaAddress> ret;
        try {
            GrafanaAddress addr = this.grafanaService.getAddress();
            ret = SkynetApiResponse.success(addr);
        } catch (Exception e) {
            ret = SkynetApiResponse.fail(e);
        }
        return ret;
    }

    /**
     * reorder agent by agent ip list
     *
     * @param order Agent ip list
     */
    private void reorderAgent(List<String> order) {
        int index = 1;
        for (String ip : order) {
            AntServerParam antServerParam = this.antConfigService.getServerParam(ip);
            antServerParam.setIndex((index++) * 10);
            this.antConfigService.setServerParam(antServerParam);
        }
    }

    /**
     * reorder actions which belong to plugin
     *
     * @param plugin     plugin code
     * @param actionList actionCode list
     */
    private void reorderAction(String plugin, List<String> actionList) {
        Assert.hasText(plugin, ApiRequestErrorCode.NULL_PARAM.getValue());
        int index = 1;
        for (String action : actionList) {
            String actionNodePath = this.antConfigService.getActionPath(plugin, action);
            this.antConfigService.setData(String.format("%s/_index", actionNodePath), String.valueOf((index++) * 10));
        }
    }
}
