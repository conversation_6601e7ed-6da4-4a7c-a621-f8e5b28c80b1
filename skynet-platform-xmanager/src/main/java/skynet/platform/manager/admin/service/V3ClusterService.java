package skynet.platform.manager.admin.service;

import skynet.platform.feign.model.ClusterInfoDto;

import java.io.IOException;

/**
 * <AUTHOR> by jianwu6 on 2020/7/28 16:54
 */
public interface V3ClusterService extends V3BaseService {

    /**
     * 获取获取集群信息
     *
     * @return
     */
    ClusterInfoDto getInfo();

    /**
     * 获取集群级属性配置
     *
     * @return
     */
    String getProperties() throws IOException;

    /**
     * 获取集群级logback日志级别配置
     *
     * @return
     */
    String getLoggingLevels() throws IOException;

    /**
     * 更新集群级属性配置
     *
     * @return
     */
    void updateProperties(String properties);

    /**
     * 更新集群级logback日志级别配置
     *
     * @return
     */
    void updateLoggingLevels(String loggingLevels);

}
