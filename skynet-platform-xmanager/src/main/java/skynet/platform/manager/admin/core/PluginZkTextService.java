package skynet.platform.manager.admin.core;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.boot.common.utils.DateUtil;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.SkynetConfigType;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.CommentProperties;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.PluginNotExistException;

import java.io.IOException;
import java.util.*;


/**
 * 插件ZooKeeper配置文本导入导出服务
 *
 * <p>该服务负责Skynet平台插件配置的导入和导出功能，支持将ZooKeeper中的
 * 插件配置以文本格式进行备份和恢复。在导出时会包含集群级别的属性信息，
 * 在导入时会智能合并集群级别的属性配置。</p>
 *
 * <p>主要功能：</p>
 * <ul>
 *   <li>配置导出 - 将插件的ZooKeeper配置导出为文本格式</li>
 *   <li>配置导入 - 从文本格式恢复插件配置到ZooKeeper</li>
 *   <li>集群属性处理 - 智能处理集群级别的配置属性</li>
 *   <li>批量操作 - 支持多个插件的批量导入导出</li>
 *   <li>配置合并 - 导入时与现有配置进行智能合并</li>
 * </ul>
 *
 * <p>配置格式特性：</p>
 * <ul>
 *   <li>集群名称占位符 - 使用{CLUSTER_NAME}占位符支持跨集群迁移</li>
 *   <li>层次结构保持 - 保持ZooKeeper节点的层次结构</li>
 *   <li>元数据包含 - 包含节点的创建时间、版本等元数据</li>
 *   <li>编码处理 - 正确处理中文和特殊字符编码</li>
 * </ul>
 *
 * <p>使用场景：</p>
 * <ul>
 *   <li>环境迁移 - 在不同环境间迁移插件配置</li>
 *   <li>配置备份 - 定期备份重要的插件配置</li>
 *   <li>版本管理 - 将配置纳入版本控制系统</li>
 *   <li>灾难恢复 - 在系统故障后快速恢复配置</li>
 *   <li>配置同步 - 在多个集群间同步配置</li>
 * </ul>
 *
 * <p>注意事项：</p>
 * <ul>
 *   <li>导出时会自动替换集群名称为占位符</li>
 *   <li>导入时会根据当前集群名称替换占位符</li>
 *   <li>集群级别属性会与现有属性进行合并而非覆盖</li>
 *   <li>操作前建议先备份现有配置</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 3.4.15
 * @see IAntConfigService ZooKeeper配置服务接口
 * @see PluginNotExistException 插件不存在异常
 */
@Slf4j
@Service
public class PluginZkTextService {

    /**
     * 集群名称占位符，用于支持跨集群配置迁移
     */
    private static final String CLUSTER_NAME_SYMBOL = "/{CLUSTER_NAME}/";

    /**
     * ZooKeeper配置服务，用于读取和写入配置数据
     */
    private final IAntConfigService antConfigService;

    /**
     * 构造函数，注入ZooKeeper配置服务
     *
     * @param antConfigService ZooKeeper配置服务实例
     */
    public PluginZkTextService(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    /**
     * 导出单个插件的ZooKeeper配置
     *
     * <p>这是一个便捷方法，用于导出单个插件的配置。
     * 内部调用批量导出方法来处理单个插件。</p>
     *
     * @param pluginCode 插件代码，用于标识要导出的插件
     * @return 插件配置的文本格式字符串
     * @throws PluginNotExistException 当指定的插件不存在时抛出
     */
    public String exportZkConfig(String pluginCode) throws PluginNotExistException {
        return this.exportZkConfig(Collections.singletonList(pluginCode));
    }

    /**
     * 批量导出ActionPoint或子系统（插件）的ZooKeeper配置定义
     *
     * <p>该方法将指定插件的完整ZooKeeper配置树导出为文本格式，
     * 包括所有子节点的配置数据。导出的配置可用于备份、迁移或版本控制。</p>
     *
     * <p>导出内容包括：</p>
     * <ul>
     *   <li>插件基本定义 - 插件的基础配置信息</li>
     *   <li>服务定义 - 插件下所有服务的配置</li>
     *   <li>属性配置 - 插件相关的属性设置</li>
     *   <li>日志配置 - 插件的日志级别配置</li>
     *   <li>集群配置 - 集群级别的配置信息</li>
     * </ul>
     *
     * <p>导出特性：</p>
     * <ul>
     *   <li>层次结构保持 - 保持ZooKeeper节点的完整层次结构</li>
     *   <li>集群名称替换 - 自动将集群名称替换为占位符，便于跨集群迁移</li>
     *   <li>编码处理 - 正确处理中文和特殊字符</li>
     *   <li>格式化输出 - 生成易读的文本格式</li>
     * </ul>
     *
     * @param codes 要导出的插件代码列表，支持批量导出多个插件
     * @return 所有指定插件配置的文本格式字符串，包含完整的配置树结构
     * @throws PluginNotExistException 当列表中任何一个插件不存在时抛出
     */
    public String exportZkConfig(List<String> codes) throws PluginNotExistException {

        Map<String, String> allMap = new TreeMap<>();
        boolean isPlugin = true;
        for (String code : codes) {
            String path = "";
            //判断是 plugin  还是 actionPoint
            if (code.indexOf("@") > 0) {
                ActionNameContract actionNameContract = new ActionNameContract(code);
                path = this.antConfigService.getActionPath(actionNameContract.getPluginCode(), actionNameContract.getActionCode());
                isPlugin = false;
            } else {
                NodeDescription nodeDescription = this.antConfigService.getPlugin(code);
                if (nodeDescription == null) {
                    throw new PluginNotExistException(code);
                }
                log.debug("exportZkConfig plugin={}", code);
                path = String.format("%s/%s", this.antConfigService.getSkynetPluginPath(), code);
                isPlugin = true;
            }

            Map<String, String> pluginData = this.antConfigService.getZkConfigService().exportData(path);
            allMap.putAll(pluginData);
        }

        //如果有插件，就包含全局的属性配置   不包含全局属性配置，因为会带来副作用。 by lyhu   2021年09月27日12:07:01
//        if (isPlugin) {
//            String settingPath = this.antConfigService.getSettingPath(null, null);
//            Map<String, String> globalMap = this.antConfigService.getZkConfigService().exportData(settingPath);
//            globalMap.forEach((k, v) -> allMap.put(k, v));
//        }

        StringBuilder stringBuilder = new StringBuilder();
        String exportDateTime = DateUtil.getCurrentDateInfo();
        String zkServers = this.antConfigService.getZookeeperServers();
        String exportDesc = String.format("# Dumped at %s: zookeeper[%s]\r\n", exportDateTime, zkServers);
        stringBuilder.append(exportDesc);
        allMap.forEach((k, v) -> stringBuilder.append(k.replaceFirst(String.format("/%s/", this.antConfigService.getClusterName()), CLUSTER_NAME_SYMBOL))
                .append("=").append(v).append("\r\n"));
        log.trace("codes={};zkConfig={}\r\n", codes, stringBuilder);
        return stringBuilder.toString();
    }


    /**
     * 导入服务定义文件
     *
     * @param zkConfigLines
     * @throws IOException
     * @throws InterruptedException
     */
    public void importZkConfig(String zkConfigLines) throws IOException, InterruptedException {
        List<String> objList = Arrays.asList(zkConfigLines.split(System.lineSeparator()));
        this.importZkConfig(objList);
    }

    public void importZkConfig(List<String> zkConfigLines) throws IOException, InterruptedException {

        String replacement = String.format("/%s/", this.antConfigService.getClusterName());
        String settingPath = this.antConfigService.getSettingPath();
        String loggerPath = String.format("%s=%s=", settingPath, SkynetConfigType.LOGGER.getContextPath());
        String propertiesPath = String.format("%s=%s=", settingPath, SkynetConfigType.PROPERTY.getContextPath());

        List<String> lineList = new ArrayList<>();
        for (String config : zkConfigLines) {
            String line = config.replace(CLUSTER_NAME_SYMBOL, replacement);
            if (line.startsWith(propertiesPath)) {
                line = combineProps(settingPath, SkynetConfigType.PROPERTY, line, line.replaceFirst(propertiesPath, ""));
            } else if (line.startsWith(loggerPath)) {
                line = combineProps(settingPath, SkynetConfigType.LOGGER, line, line.replaceFirst(loggerPath, ""));
            }
            if (StringUtils.isNoneBlank(line)) {
                lineList.add(line);
            }
        }
        this.antConfigService.getZkConfigService().importData(lineList, true);
    }

    private String combineProps(String settingPath, SkynetConfigType skynetConfigType, String thisLine, String thisProps) throws IOException {

        //本次导入 全局属性 为空，不导入
        if (StringUtils.isBlank(thisProps)) {
            return null;
        }
        //获取已经存在的全局属性，如果为空，直接使用本次的属性
        String path = String.format("%s/%s", settingPath, skynetConfigType.getContextPath());
        String lastProps = this.antConfigService.getData(path);
        if (StringUtils.isBlank(lastProps)) {
            return thisLine;
        }

        CommentProperties lastProperties = new CommentProperties();
        lastProperties.load(lastProps);
        CommentProperties thisProperties = new CommentProperties();
        thisProps = thisProps.replaceAll("\\\\n", System.lineSeparator());
        thisProperties.load(thisProps);

        //追加替换
        thisProperties.getAllProperty().forEach((k, v) -> lastProperties.setProperty(k, v, thisProperties.getComment(k)));

        return String.format("%s=%s=%s", settingPath, skynetConfigType.getContextPath(), lastProperties.store());
    }
}
