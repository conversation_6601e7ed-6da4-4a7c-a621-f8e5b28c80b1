package skynet.platform.manager.admin.core;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import lombok.extern.slf4j.Slf4j;
import skynet.boot.websocket.WebSocketConfig;
import skynet.platform.common.logging.LogFileWatcher;
import skynet.platform.common.logging.LogbackConfig;

import java.io.File;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * 在 skynet/log 目录下找 agent_deploy_${targetIP}.log 文件
 * 把文件内容通过  ws 发给调用者
 *
 * <AUTHOR>
 */
@Slf4j
public class DeployWebsocket {

    private LogFileWatcher logFileWatcher;


    @OnOpen
    public void onOpen(@PathParam("ip") String ip, Session session) throws IOException {

        try {
            InetSocketAddress clientHostIp = WebSocketConfig.getRemoteAddressBySession(session);
            log.debug("New Connected [sessionId:{}][from:{}][ip:{}] ... ", session.getId(), clientHostIp, ip);

            ip = URLDecoder.decode(ip, StandardCharsets.UTF_8);
            String logFileName = LogbackConfig.getAgentDeployLogFullFilePath(ip);

            File agentDeployLogFile = new File(logFileName);
            if (!agentDeployLogFile.exists()) {
                log.warn("the agent deploy file fails to be opened. the file does not exist, file name={}", logFileName);
                session.getBasicRemote().sendText(String.format("[logServer connected.]The deploy logfile does not exist.[%s]", logFileName));
                session.close(new CloseReason(CloseReason.CloseCodes.NORMAL_CLOSURE, String.format("not thereunto IP[%s] node distributes the installation package", ip)));
                return;
            }

            log.debug("LogFileName:{}", logFileName);
            session.getBasicRemote().sendText(String.format("[logServer connected.][%s]", logFileName));

            logFileWatcher = new LogFileWatcher(agentDeployLogFile, session);
            logFileWatcher.watch();
        } catch (Exception e) {
            log.error("Agent installation log websocket onOpen error", e);
            throw e;
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("sessionId[{}] : message[{}]", session.getId(), message);

        if (logFileWatcher != null) {
            logFileWatcher.isPause("pause".equals(message));
        }
    }

    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        log.debug("Session {} closed because of {}", session.getId(), closeReason);
        try {
            if (logFileWatcher != null) {
                logFileWatcher.close();
            }
        } catch (Exception e) {
            log.error("close error.", e);
        }
    }

    @OnError
    public void onError(Session session, Throwable t) {
        try {
            log.debug("LogPushServer WebSocket onError.{}", t.getMessage());
            if (logFileWatcher != null) {
                logFileWatcher.close();
            }
        } catch (Exception e) {
            log.error("close error.");
        }
    }
}
