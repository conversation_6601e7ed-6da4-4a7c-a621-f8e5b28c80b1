package skynet.platform.manager.admin.controller;

import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.Scope;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;
import skynet.boot.websocket.HttpSessionConfigurator;
import skynet.platform.manager.admin.core.DeployWebsocket;

/**
 * 在 skynet/log 目录下找 agent_deploy_${targetIP}.log 文件
 * 把文件内容通过  ws 发给调用者
 *
 * <AUTHOR>
 */
@Slf4j

@Profile("!mock")
@Component
@ServerEndpoint(value = "/skynet/api/v3/agents/installation/{ip}/log", configurator = HttpSessionConfigurator.class)
@Scope(scopeName = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class V3InstallationWebSocket extends DeployWebsocket {

}
