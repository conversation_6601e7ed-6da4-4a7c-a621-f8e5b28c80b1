package skynet.platform.manager.admin.service.k8s.boot;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import skynet.boot.SkynetProperties;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.env.BootEnvironmentBuilder;

import java.util.Collections;
import java.util.List;

/**
 * docker程序启动
 *
 * <AUTHOR> QQ：408365330
 * @date $date$
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component(DockerBoot.BEAN_NAME)
public class DockerBoot extends BaseBoot {

    public static final String BEAN_NAME = "boot.dockerboot";

    public DockerBoot(SkynetProperties skynetProperties, BootEnvironmentBuilder bootEnvironmentBuilder) {
        super(skynetProperties, bootEnvironmentBuilder);
    }

    @Override
    protected String getToProcess(BootEnvironment bootEnvironment) {

        //替换占位符
        String imagesName = bootEnvironment.replacePlaceholder(this.getAntActionParam().getBootParam().getMainJar());
        //改用 AID，不然同一个docker不能启动多个实例
        String containerName = "skynet-" + super.getAid().replace("@", "-");

        String cmd = String.format("docker start %s -i", containerName);
        log.info("Exec cmd:\t[{}]", cmd);
        log.info("===============End assembling the docker command===========");
        return cmd;
    }


    /**
     * 如果是Dockerboot，将workArgs 置空，自己按照用户原样拼接
     *
     * @return
     * @throws Exception
     */
    @Override
    public List<String> getWorkArgs() throws Exception {
        return Collections.emptyList();
    }


    private String envNormalize(String env) {
        if (StringUtils.isEmpty(env)) {
            return "";
        }
        String ret = super.getAppEnvironment().replacePlaceholder(env);
        ret = ret.replaceAll("\\s+", " ");
        return ret;
    }
}
