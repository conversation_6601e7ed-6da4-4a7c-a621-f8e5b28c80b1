package skynet.platform.manager.admin.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.common.repository.OnlineActionManager;
import skynet.platform.common.repository.domain.AntActionStatus;
import skynet.platform.feign.model.xray.*;
import skynet.platform.feign.service.V3SysInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 主机系统状态信息，调用 统计 集群中每台服务器的信息
 *
 * <AUTHOR>
 */

@Slf4j
@RestController
@ExposeSwagger2

public class V3SysInfoController implements V3SysInfo {

    private final OnlineActionManager onlineActionManager;
    private final RestTemplate authRestTemplate;

    public V3SysInfoController(OnlineActionManager onlineActionManager, @Qualifier("authRestTemplate") RestTemplate authRestTemplate) {
        this.onlineActionManager = onlineActionManager;
        this.authRestTemplate = authRestTemplate;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public ServerSummary getSysSummary() throws Exception {
        AntActionStatus antActionStatus = onlineActionManager.getNode(AppBootEnvironment.AGENT_ACTION_POINT, true);
        return this.queryAgent(antActionStatus, "summary", ServerSummary.class);
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public List<DiskStat[]> getDiskStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception {
        return this.query(ip, "disk", DiskStat[].class);
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public List<NetworkStat[]> getNetworkStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception {
        return this.query(ip, "network", NetworkStat[].class);

    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public List<GpuStat[]> getGpuStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception {
        return this.query(ip, "gpu", GpuStat[].class);

    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public List<CpuStat> getCpuStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception {
        return this.query(ip, "cpu", CpuStat.class);
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public List<MemStat> getMemStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception {
        return this.query(ip, "mem", MemStat.class);
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public List<ConnectionStat> getConnectionStats(@RequestParam(name = "ip", required = false, defaultValue = "") String ip) throws Exception {
        return this.query(ip, "connection", ConnectionStat.class);
    }

    private <T> List<T> query(String ip, String target, Class<T> responseType) throws Exception {
        List<AntActionStatus> antActionStatusList = onlineActionManager.getAllNodes(AppBootEnvironment.AGENT_ACTION_POINT);
        List<T> objList = new ArrayList<>(antActionStatusList.size());
        for (AntActionStatus antActionStatus : antActionStatusList) {
            if (StringUtils.isBlank(ip) || antActionStatus.getIp().equals(ip)) {
                try {
                    objList.add(this.queryAgent(antActionStatus, target, responseType));
                } catch (Exception e) {
                    log.error("Query ip={} target={} error={}", antActionStatus.getIp(), target, e.getMessage());
                }
            }
        }
        return objList;
    }


    private <T> T queryAgent(AntActionStatus antActionStatus, String target, Class<T> responseType) throws Exception {
        if (antActionStatus == null) {
            throw new RuntimeException("the antActionStatus is null.");
        }
        String uri = String.format("http://%s:%d/skynet/agent/sysinfo/%s", antActionStatus.getIp(), antActionStatus.getPort(), target);
        log.debug("Query target={} by uri={}", target, uri);
        return authRestTemplate.getForObject(uri, responseType);
    }
}
