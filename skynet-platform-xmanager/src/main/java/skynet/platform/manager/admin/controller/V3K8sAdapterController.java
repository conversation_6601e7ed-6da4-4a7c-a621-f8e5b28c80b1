package skynet.platform.manager.admin.controller;


import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.ActionDefinitionDto;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sAdapter;
import skynet.platform.manager.admin.service.k8s.K8sDependResourceService;

import java.io.IOException;

/**
 * K8s 适配器
 */
@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2

public class V3K8sAdapterController implements V3K8sAdapter {

    private final K8sDependResourceService k8sDependResourceService;

    public V3K8sAdapterController(K8sDependResourceService k8sDependResourceService) {
        this.k8sDependResourceService = k8sDependResourceService;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<ActionDefinitionDto> fetchDefinition(K8sDependResourceRequest k8sDependResourceRequest) throws IOException {

        log.debug("getDefinition k8sDependResourceRequest={}", k8sDependResourceRequest);
        SkynetApiResponse<ActionDefinitionDto> response = new SkynetApiResponse<>();
        try {
            response.setData(k8sDependResourceService.fetchDefinition(k8sDependResourceRequest));
        } catch (Exception e) {
            log.error("getDefinition k8sDependResourceRequest={}; error={}", k8sDependResourceRequest, e.getMessage());
            response.setCode(-1);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public SkynetApiResponse<K8sDependResourceResponse> fetchDependResource(K8sDependResourceRequest k8sDependResourceRequest) {
        log.debug("getDependResource k8sDependResourceRequest={}", k8sDependResourceRequest);
        SkynetApiResponse<K8sDependResourceResponse> response = new SkynetApiResponse<>();
        try {
            response.setData(k8sDependResourceService.fetchResource(k8sDependResourceRequest));
        } catch (Exception e) {
            log.error("getDependResource k8sDependResourceRequest={}; error={}", k8sDependResourceRequest, e.getMessage());
            response.setCode(-1);
            response.setMessage(e.getMessage());
        }
        return response;
    }
}
