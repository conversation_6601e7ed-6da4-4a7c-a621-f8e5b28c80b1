# skynet-3.4.x 部署手册

## 
## 一、 部署包准备

依赖：

* [zookeeper-3.9.3-bin.tar.gz](http://turing.iflytek.com:1180/repo/component/zookeeper-3.9.3-bin.tar.gz)
* [zkui2.0_with_acl.tar.gz](http://turing.iflytek.com:1180/repo/component/zkui2.0_with_acl.tar.gz) 
* skynet开发版本获取地址：http://turing.iflytek.com:1180/repo/skynet/3.4.x

目前最新RELEASE版本为 3.4.15，请到图聆产品货架申请。申请时请注意填写平台架构: x86_64 / ARM64v8(aarch64)。

请申请使用最新的skynet版本。[Release Notes](../changelog.md)

## 二、环境准备
### 1、hosts配置
部署环境的/etc/hosts需要添加ip与主机名的设置，如下面所示
```shell
127.0.0.1 localhost
${ip}  ${hostname}
```
### 2、关闭防火墙
Skynet环境需要关闭防火墙，不同系统，防火墙关闭与查看方式统统，下面列出几种主要系统的关闭防火墙命令
```shell
# 查看防火墙的状态：
service iptables status
# 关闭防火墙：
service iptables stop
```
CentOS 7.x
```shell
# 查看防火墙的状态：
systemctl status firewalld
# 关闭防火墙：
service firewalld stop
```
Ubuntu
```shell
# 查看防火墙的状态：
ufw status
# 关闭防火墙：
ufw disable
```
### 3、系统参数调整
linux端口TIME_WAIT以及close_wait过多和禁用 IPv6
```shell
vim /etc/sysctl.conf
```
配置内容
```bash
#linux端口TIME_WAIT以及close_wait过多
#如果存在，直接修改，不存在 在后面添加
net.ipv4.tcp_timestamps = 0
net.ipv4.tcp_tw_recycle = 1
net.ipv4.tcp_tw_reuse = 1
net.core.netdev_max_backlog = 262144
net.ipv4.tcp_max_syn_backlog = 262144
net.ipv4.tcp_max_tw_buckets = 3000
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 60

# 防止 Nginx 报错 Cannot assign requested address
net.ipv4.ip_local_port_range = 10240 65535
 
#禁用IPv6
net.ipv6.conf.all.disable_ipv6 =1
net.ipv6.conf.default.disable_ipv6 =1

 
#如果你想要为特定的网卡禁止IPv6，比如，对于enp0s3
#net.ipv6.conf.enp0s3.disable_ipv6 =1
```
说明：操作系统的中打开文件的最大句柄数受限所致，常常发生在很多个并发用户访问服务器的时候.因为为了执行每个用户的应用服务器都要加载很多文件(new一个socket就需要一个文件句柄),这就会导致打开文件的句柄的缺乏.
```shell
#查看每个用户允许打开的最大文件数，如果系统默认的是open files (-n) 1024，则需要修改
ulimit -a
  
#在系统文件/etc/security/limits.conf中修改这个数量限制，在文件中加入内容：
* soft nofile 65536
* hard nofile 65536
```
修改完成保存，重启服务器
```shell
#让配置生效
sysctl -p
 
#如果IPv6未禁用，需要重启系统
reboot
netstat -lnpt
```

#### 最大打开文件数（Max open files）配置不生效

通过上面的方法修改完系统参数后，使用 `ulimit -a` 确认最大打开文件数是否生效：

```
$ ulimit -a | grep "open files"
```

同时使用下面的命令确认对指定进程是否生效：

```
$ cat /proc/<pid>/limits | grep "Max open files"
```

如果 `ulimit -a` 生效，但是 `/proc/<pid>/limits` 不生效，则可能是 Skynet 的问题。

由于 Skynet v3.4.3 之后的版本中，加入了进程守护的功能，Skynet Agent 是通过 systemd 运行的，所以还需要修改 systemd 的配置文件：

```
$ vi /usr/lib/systemd/system/skynet_xagent.service
```

在 `[Service]` 节添加 `LimitNOFILE=65536` 配置：

```
[Service]
LimitNOFILE=65536
```

重启 Skynet Agent：

```
$ systemctl daemon-reload
$ systemctl restart skynet_xagent
```

然后再重启对应的服务即可。

## 三、部署操作
skynet部署前需要安装的软件有：zookeeper，zkui。zookeeper和zkui依赖jdk，所以需要在部署zookeeper的节点先安装jdk。
`说明：从2.1.0.1009版本开始，skynet安装包内置了jdk1.8，无需在skynet的服务托管节点单独安装jdk，本章节所述的jdk安装仅用于zookeeper(集群)节点。`
Skynet运行需要jdk环境，jdk版本要求为1.8.0_212。jdk由于安装较为简单，请自行安装。安装完成后，请查看java是否正常。

centos默认自带openjdk，安装前请先执行rpm -qa |grep openjdk 查询已安装的jdk rpm，用 rpm -e --nodeps 包名来卸载openjdk。  
`这里一定要注意，需要安装的是完整的jdk环境，而不仅仅是jre环境，下面是java环境查看命令`
```
#查看jre版本
java -version
#查看jdk版本
javac -version
```

如果不是jdk1.8，重新下载安装jdk1.8：  
x86_64平台：http://turing.iflytek.com:1180/repo/component/jdk1.8.0_261.tar.gz  
ARM64平台：http://turing.iflytek.com:1180/repo/component/jdk-8u212-linux-arm64-vfp-hflt.tar.gz

```
root@u167:/usr# mkdir /usr/java
root@u167:/usr# cp /iflytek/software/jdk1.8.0_261.tag.gz /usr/java
root@u167:/usr# cd /usr/java
root@u167:/usr/java# tar -xzvf jdk1.8.0_261.tag.gz
```

配置JAVA_HOME环境变量 `vim /etc/profile`,在尾部添加  
```
export JAVA_HOME=/usr/java/jdk1.8.0_261
export JRE_HOME=$JAVA_HOME/jre
export CLASSPATH=.:$JAVA_HOME/lib:$JRE_HOME/lib:$CLASSPATH
export PATH=$JAVA_HOME/bin:$JRE_HOME/bin:$JAVA_HOME:$PATH
```
使修改生效
```
root@u167:~# source /etc/profile
```
再检查java_version
```
root@u167:~# java -version
java version "1.8.0_212"
Java(TM) SE Runtime Environment (build 1.8.0_212-b15)
Java HotSpot(TM) 64-Bit Server VM (build 25.71-b15, mixed mode)
```
### 1、无进程守护和开机自启方案安装
#### 安装zookeeper
1、zookeeper解压
```shell
tar -zxvf zookeeper-3.6.x-bin.tar.gz
```

2、解压后进入apache-zookeeper-3.6.x-bin/conf目录下进行改配查看conf目录下有无jaas.cfg和java.env文件（一般是有的），如果没有，从skynet/doc/zk_svc_conf/目录下拷贝过去即可。zoo.cfg添加如下两行内容（一般是添加好的）：
```shell
sessionRequireClientSASLAuth=true
authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider
```

3、启动zookeeper 
```shell
cd apache-zookeeper-3.6.x-bin/
./zkServer.sh start
```

4、验证

zookeeper默认占用端口：2181。可以通过如下方式验证：

1）、验证端口
```shell
netstat -nlp | grep 2181
```
2）、验证进程
```shell
ps -ef | grep zookeeper
```

5、运维

1）、停止zookeeper
```shell
./zkServer.sh stop
```
2）、重启zookeeper
```shell
./zkServer.sh restart
```
#### 安装zkui（可选）
1、zkui解压
```shell
tar -zxvf zkui2.0_with_acl.tar.gz
```
2、解压后进入zkui/目录后查看有无skynet.zk.jaas.cfg文件。如果没有，可以从skynet的解压目录下conf/目录下拷贝过来。

3、启动zkui
```shell
nohup ./start.sh &
```
4、验证

zkui默认占用端口：9090。可以通过如下方式验证：

1）、验证端口
```shell
netstat -nlp | grep 9090
```
2）、验证进程
```shell
ps -ef | grep zkui
```
3）、登录页面验证

网页：http://{ip}:9090  默认用户名/密码：admin/manager或者admin/admin,可参考配置文件config.cfg中userSet处配置

5、运维

1）、停止zkui
```shell
./stop.sh
```
#### 安装skynet

1、skynet解压
```shell
tar -zxvf skynet-3.4.x-xxxx.tar.gz
```
2、配置，进入skynet/conf目录下

查看conf目录下有无jaas.cfg和java.env文件（一般是有的），如果没有，从skynet/doc/zk_svc_conf/目录下拷贝过去即可。

3、修改conf/application.properties文件。

将该行的注释去掉：  
```shell
skynet.append.jvm.options.props=java.security.auth.login.config
```
4、修改conf/skynet.properties文件。

将ip地址替换成zookeeper服务部署所在服务器的ip地址：	
```shell
skynet.zookeeper.server_list=127.0.0.1:2181
```
并去掉改行的注释：
```shell
java.security.auth.login.config=${SKYNET_HOME}/conf/skynet.zk.jaas.cfg
```
5、重命名或者删除进程守护配置文件
将skynet/conf/skynet.service文件重命名或者删除掉。

6、启动skynet
```shell
./ant-xmanager.sh daemon     (./ant-xmanager.sh start 为前台启动，用于排查错误)
```
7、验证

skynet默认占用端口：2230。可以通过如下方式验证：
	
1）、验证端口
```shell
netstat -nlp | grep 2230
```
2）、验证进程
```shell
ps -ef | grep skynet
```
3）、登录页面验证

网页：http://{ip}:2230  默认用户名/密码：admin/Skynet@2230

7、运维

1）、停止skynet
```shell
./ant-xmanager stop
```
### 2、进程守护和开机自启方案安装
该方案提供进程守护和开机自启功能。但需要root权限。如果不需要此功能，或者无法满足root权限，请选择“无守护和开机自启方案安装”章节进行安装
#### 安装zookeeper
1、zookeeper解压
```shell
tar -zxvf zookeeper-3.6.x-bin.tar.gz
```
2、解压后进入apache-zookeeper-3.6.x-bin/conf目录下进行改配

查看conf目录下有无jaas.cfg和java.env文件（一般是有的），如果没有，从skynet/doc/zk_svc_conf/目录下拷贝过去即可。zoo.cfg添加如下两行内容（一般是添加好的）：
```shell
sessionRequireClientSASLAuth=true
authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider
```
3、启动zookeeper 
```shell
cd apache-zookeeper-3.6.x-bin/
./watchdog_start.sh
```
4、验证

zookeeper默认占用端口：2181。可以通过如下方式验证：

1）、验证端口
```shell
netstat -nlp | grep 2181
```
2）、验证进程
```shell
systemctl status zookeeper
```
5、运维（执行systemctl命令时，需要root用户，无需进入任何目录）

1）、停止zookeeper
```shell
systemctl stop zookeeper
```
2）、再次启动zookeeper（注意区分首次启动和再次启动的区别，首次启动只能用watchdog_start.sh脚本启动）
```shell
systemctl start zookeeper  或者使用首次启动的脚本watchdog_start.sh也可以。
```
3）、重启zookeeper
```shell
systemctl restart zookeeper
```
4）、查看zookeeper服务状态
```shell
systemctl status zookeeper -l
```
#### 安装zkui
1、zkui解压
```shell
tar -zxvf zkui2.0_with_acl.tar.gz
```
2、解压后进入zkui/目录后查看有无skynet.zk.jaas.cfg文件。如果没有，可以从skynet的解压目录下conf/目录下拷贝过来。

3、启动zkui
```shell
./watch_start.sh
```
4、验证

zkui默认占用端口：9090。可以通过如下方式验证：

1）、验证端口
```shell
netstat -nlp | grep 9090
```
2）、验证进程
```shell
systemctl status zkui
```
3）、登录页面验证

网页：http://{ip}:9090  默认用户名/密码：admin/manager

5、运维（执行systemctl命令时，需要root用户，无需进入任何目录）

1）、停止zkui
```shell
systemctl stop zkui
```
2）、再次启动zkui（注意区分首次启动和再次启动的区别，首次启动只能用watchdog_start.sh脚本启动）
```shell
systemctl start zkui或者使用首次启动的脚本watchdog_start.sh也可以。
```
3）、重启zkui
```shell
systemctl restart zkui
```
4）、查看zkui服务状态
```shell
systemctl status zkui  -l
```
#### 安装skynet
1、skynet解压
```shell
tar -zxvf skynet-3.4.x-xxxx.tar.gz
```
2、配置，进入skynet/conf目录下

查看conf目录下有无jaas.cfg和java.env文件（一般是有的），如果没有，从skynet/doc/zk_svc_conf/目录下拷贝过去即可。

3、修改conf/application.properties文件。

将该行的注释去掉：  
```shell
skynet.append.jvm.options.props=java.security.auth.login.config
```
4、修改conf/skynet.properties文件。

将ip地址替换成zookeeper服务部署所在服务器的ip地址：
```shell
skynet.zookeeper.server_list=127.0.0.1:2181
```
并去掉改行的注释：
```shell
java.security.auth.login.config=${SKYNET_HOME}/conf/skynet.zk.jaas.cfg
```
5、启动skynet
```shell
./ant-xmanager.sh daemon   (./ant-xmanager.sh start 为前台启动，用于排查错误，且这种启动方式不会有进程守护和开机自启的功能)
```
6、验证

skynet默认占用端口：2230。可以通过如下方式验证：

1）、验证端口
```shell
netstat -nlp | grep 2230
```
2）、验证进程
```shell
systemctl status skynet
```
3）、登录页面验证

网页：http://{ip}:2230  默认用户名/密码：admin/Skynet@2230

7、运维（执行systemctl命令时，需要root用户，无需进入任何目录）

1）、停止skynet
```shell
systemctl stop skynet
```
2）、再次启动skynet（注意区分首次启动和再次启动的区别，首次启动只能用watchdog_start.sh脚本启动）
```shell
systemctl start skynet或者使用首次启动的脚本watchdog_start.sh也可以。
```
3）、重启skynet
```shell
systemctl restart skynet
```
4）、查看skynet服务状态
```shell
systemctl status skynet -l
```

## 四、版本升级

### 1、skynet3.x版本
升级步骤：

1.将待升级的skynet tar 包在到 xmanager 所在的服务器的一个目录 如：/iflytek/server/skynet-3.4.2-x86_64-Build1779.tar.gz
2.切换到正在运行的skynet部署包目录：skynet/bin 目录，执行

```shell
# 假设 部署目录是  /iflytek/server/skynet
cd /iflytek/server/skynet/bin
./upgrade.sh  /iflytek/server/skynet-3.4.2-x86_64-Build1779.tar.gz
```

3.再重新 切到 skynet 部署目录，重启启动 xmanager
```shell
# 假设 部署目录是  /iflytek/server/skynet
cd /iflytek/server/skynet/bin
./ant-xmanager.sh daemon
```
> 备注，升级的过程，不会影响对已经运行托管的服务。

### 2、skynet2.x版本
如果是skynet2.x 升级到 skynet3.x，建议步骤：
1.通过zkui备份以前的zk配置，防止配置损坏。
2.解压 skynet-3.4.2-x86_64-Build1779.tar.gz 部署包，到skynet/doc 目录下，找到 `skynet3.x-upgrade.zk.config` 配置文件，
将其导入到zk中，注意，默认的集群名是 skynet,如果不是，建议打开文件重新替换。
3.替换除 repo 和 runtime 目录，然后修改zk的配置，与上面的全新部署步骤一样。
