package skynet.platform.common;

import skynet.platform.common.domain.SkyosCluster;
import skynet.platform.common.utils.SkyosClusterUtils;

import java.io.IOException;
import java.util.List;

/**
 * Cluster file解析工具使用示例
 */
public class SkyosClusterExampleTest {
    
    public static void main(String[] args) {
        // 示例YAML内容
        String yamlContent = """
            apiVersion: apps.skyos.io/v1beta1
            kind: Cluster
            metadata:
              name: skyos-ubuntu
            spec:
              hosts:
                - ips:
                    - *************
                  roles:
                    - master
                - ips:
                    - *************
                  roles:
                    - worker
              image:
                - "@/images/kubernetes_amd64_v1.29.9.tar"
                - "@/images/cilium_amd64_v1.13.4.tar"
                - "@/images/helm_amd64_v3.9.4.tar"
              ssh:
                passwd: Dc@2024%b3&A2
                pk: /root/.ssh/id_rsa
                port: 22
                user: root
            """;
        
        try {
            // 解析YAML字符串
            System.out.println("=== 解析Clusterfile ===");
            SkyosCluster config = SkyosClusterUtils.parseFromString(yamlContent);
            
            // 验证配置
            System.out.println("=== 验证配置 ===");
            SkyosClusterUtils.ValidationResult validation = SkyosClusterUtils.validateConfig(config);
            if (validation.isValid()) {
                System.out.println("✓ 配置验证通过");
            } else {
                System.out.println("✗ 配置验证失败:");
                System.out.println(validation);
            }
            
            // 获取集群信息
            System.out.println("\n=== 集群信息 ===");
            System.out.println("集群名称: " + config.getMetadata().getName());
            System.out.println("API版本: " + config.getApiVersion());
            System.out.println("类型: " + config.getKind());
            
            // 获取节点信息
            List<String> masterIps = SkyosClusterUtils.getMasterIps(config);
            List<String> workerIps = SkyosClusterUtils.getWorkerIps(config);
            List<String> allIps = SkyosClusterUtils.getAllIps(config);
            
            System.out.println("\n=== 节点信息 ===");
            System.out.println("Master节点: " + masterIps);
            System.out.println("Worker节点: " + workerIps);
            System.out.println("所有节点: " + allIps);
            
            // 获取SSH配置
            System.out.println("\n=== SSH配置 ===");
            SkyosCluster.Ssh ssh = config.getSpec().getSsh();
            System.out.println("用户: " + ssh.getUser());
            System.out.println("端口: " + ssh.getPort());
            System.out.println("私钥路径: " + ssh.getPk());
            System.out.println("密码: " + (ssh.getPasswd() != null ? "已设置" : "未设置"));
            
            // 获取镜像信息
            System.out.println("\n=== 镜像信息 ===");
            List<String> images = config.getSpec().getImage();
            for (int i = 0; i < images.size(); i++) {
                System.out.println("镜像 " + (i + 1) + ": " + images.get(i));
            }
            
            // 转换为YAML字符串
            System.out.println("\n=== 转换回YAML ===");
            String generatedYaml = SkyosClusterUtils.toYaml(config);
            System.out.println(generatedYaml);
            
        } catch (IOException e) {
            System.err.println("解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示从文件解析
     */
    public static void parseFromFile(String filePath) {
        
        try {
            SkyosCluster config = SkyosClusterUtils.parseFromFile(filePath);
            System.out.println("成功解析文件: " + filePath);
            System.out.println("集群名称: " + config.getMetadata().getName());
            
            // 验证配置
            SkyosClusterUtils.ValidationResult validation = SkyosClusterUtils.validateConfig(config);
            if (validation.isValid()) {
                System.out.println("配置验证通过");
            } else {
                System.out.println("配置验证失败:");
                System.out.println(validation);
            }
            
        } catch (IOException e) {
            System.err.println("文件解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 演示创建新的配置
     */
    public static SkyosCluster createNewConfig() {
        SkyosCluster config = new SkyosCluster();
        
        // 设置基本信息
        config.setApiVersion("apps.skyos.io/v1beta1");
        config.setKind("Cluster");
        
        // 设置metadata
        SkyosCluster.Metadata metadata = new SkyosCluster.Metadata();
        metadata.setName("my-cluster");
        config.setMetadata(metadata);
        
        // 设置spec
        SkyosCluster.Spec spec = new SkyosCluster.Spec();
        
        // 设置hosts
        SkyosCluster.Host masterHost = new SkyosCluster.Host();
        masterHost.setIps(List.of("*************"));
        masterHost.setRoles(List.of("master"));
        
        SkyosCluster.Host workerHost = new SkyosCluster.Host();
        workerHost.setIps(List.of("*************", "*************"));
        workerHost.setRoles(List.of("worker"));
        
        spec.setHosts(List.of(masterHost, workerHost));
        
        // 设置镜像
        spec.setImage(List.of(
            "@/images/kubernetes_amd64_v1.29.9.tar",
            "@/images/cilium_amd64_v1.13.4.tar"
        ));
        
        // 设置SSH
        SkyosCluster.Ssh ssh = new SkyosCluster.Ssh();
        ssh.setUser("root");
        ssh.setPort(22);
        ssh.setPasswd("password123");
        ssh.setPk("/root/.ssh/id_rsa");
        spec.setSsh(ssh);
        
        config.setSpec(spec);
        
        return config;
    }
} 