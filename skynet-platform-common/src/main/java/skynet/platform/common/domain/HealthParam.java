package skynet.platform.common.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * 健康检测参数，
 *
 * <pre>
 * 初次检测时间。 缺省 10秒;
 * 检测时间间隔. 缺省 30秒 ;
 * s
 * </pre>
 */
@Getter
@Setter
public class HealthParam extends Jsonable {

    /**
     * The is check health.
     */
    @JSONField(ordinal = 5)
    private boolean checkEnabled = false;

    /**
     * The protocol.
     */
    @JSONField(ordinal = 10)
    private String protocol = "http";

    /**
     * The path.
     */
    @JSONField(ordinal = 20)
    private String path = "/";

    /**
     * 初次检测时间。 缺省 10秒.
     */
    @JSONField(ordinal = 40)
    private int initialDelaySeconds = 10;

    /**
     * 检测时间间隔. 缺省 30秒
     */
    @JSONField(ordinal = 50)
    private int intervalSeconds = 30;

    /**
     * 超时时间. 缺省 20秒
     */
    @JSONField(ordinal = 60)
    private int timeoutSeconds = 20;

    /**
     * The retry times.
     */
    @JSONField(ordinal = 70)
    private int retryTimes = 3;

    @Override
    public String toString() {
        return super.toString();
    }

}