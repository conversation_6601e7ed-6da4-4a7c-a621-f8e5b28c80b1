package skynet.platform.common.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.common.domain.SampleState;
import skynet.platform.common.repository.domain.AntNodeState;
import skynet.platform.common.repository.domain.AntNodeUpType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class BootStatus extends Jsonable {

    @JSONField(ordinal = 5)
    private String aid;

    @JSONField(ordinal = 10)
    private String cmd;

    @JSONField(ordinal = 15)
    private String appConfigFile;

    @JSONField(ordinal = 15)
    private String meshConfigFile;

    @JSONField(ordinal = 20)
    private List<String> zkPaths = new ArrayList<>(0);

    @JSONField(ordinal = 21)
    private Map<String, Object> extProps;

    @JSONField(ordinal = 22)
    private String serverContextPath;

    @JSONField(ordinal = 25)
    private SampleState status;

    @JSONField(ordinal = 40)
    private BootProfile profile;

    @JSONField(ordinal = 50)
    private AntActionParam action;

    @JSONField(ordinal = 60)
    private AntNodeState state;

    @JSONField(ordinal = 70)
    private int bootIndex = 0;

    @JSONField(ordinal = 75)
    private List<Integer> pidChildren = new ArrayList<>(0);

    @JSONField(ordinal = 80)
    private String discoveryId;

    @JSONField(ordinal = 90)
    private boolean debug;

    public BootStatus() {
    }

    public BootStatus(BootProfile bootProfile) {
        this.profile = bootProfile;
        this.aid = bootProfile.getAid();
    }


    public void setUp(AntNodeUpType type) {
        if (state != null) {
            state.setUp(type);
        }
        if (profile != null) {
            profile.setUp(type);
        }
    }

    public void setPort(int port) {
        if (state != null) {
            state.setPort(port);
        }
        if (profile != null) {
            profile.setPort(port);
        }
    }

    public void setAppPort(int appPort) {
        if (state != null) {
            state.setAppPort(appPort);
        }
        if (profile != null) {
            profile.setAppPort(appPort);
        }
    }

    public void setExtPorts(List<Integer> extPorts) {
        if (state != null) {
            state.setExtPorts(extPorts);
        }
        if (profile != null) {
            profile.setExtPorts(extPorts);
        }
    }

    public void setPid(int pid) {
        if (state != null) {
            state.setPid(pid);
        }
        if (profile != null) {
            profile.setPid(pid);
        }
    }

    public void setProtocol(String protocol) {
        if (state != null) {
            state.setProtocol(protocol);
            state.setSsl("https".equalsIgnoreCase(protocol));
        }
    }

    public void setDown(int bootIndex) {
        this.zkPaths = null;
        this.appConfigFile = null;
        this.bootIndex = bootIndex;
        this.setUp(AntNodeUpType.DOWN);
        this.setPid(0);
    }

    @JSONField(serialize = false)
    public String getUUID() {
        return String.format("%s_%d", this.aid, this.getProfile().getPid());
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
