package skynet.platform.common.repository.config.observer;

import skynet.platform.common.repository.domain.AntActionStatus;

import java.util.List;
import java.util.Map;
import java.util.Observable;
import java.util.Observer;

public abstract class AllOnlineActionStatusObserver implements Observer {

    @SuppressWarnings("unchecked")
    @Override
    public void update(Observable o, Object arg) {
        this.update((Map<String, List<AntActionStatus>>) arg);
    }

    protected abstract void update(Map<String, List<AntActionStatus>> data);


}
