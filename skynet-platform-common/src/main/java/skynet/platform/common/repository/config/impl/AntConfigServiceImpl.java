/**
 *
 */
package skynet.platform.common.repository.config.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.boot.zookeeper.ZkConfigService;
import skynet.platform.common.domain.*;
import skynet.platform.common.exception.ActionNotExistException;
import skynet.platform.common.exception.AntException;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.observer.*;
import skynet.platform.common.repository.domain.*;
import skynet.platform.common.repository.exception.InvalidActionPointException;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Skynet Ant 在Zookeeper中配置接口 实现
 *
 * <AUTHOR>
 */
@Slf4j
public class AntConfigServiceImpl implements IAntConfigService {

    private static final String REF_PATTERN_STR = "\\{\\$ref@setting:([A-Za-z0-9_]+)\\}";
    private static final Pattern REF_PATTERN = Pattern.compile(REF_PATTERN_STR);

    /**
     * 菜单列表缓存 - 使用LoadingCache自动加载机制，Optional包装避免null值
     * 缓存策略：访问后30秒过期，最大10个条目，并发级别3
     * 菜单数据变化不频繁，可以设置较长的缓存时间
     */
    private final LoadingCache<String, Optional<List<AntMenuView>>> menuListCache;

    /**
     * /skynet
     */
    public final String SKYNET_ROOT_PATH;
    /**
     * /skynet/cluster
     */
    public final String SKYNET_CLUSTER_PATH;
    /**
     * /skynet/plugin
     */
    public final String SKYNET_PLUGIN_PATH;
    /**
     * /skynet/setting
     */
    public final String SKYNET_SETTINGS_PATH;
    /**
     * /skynet/cluster/online
     */
    public final String SKYNET_ONLINE_PATH;
    /**
     * /skynet/cluster/online/action
     */
    public final String SKYNET_ONLINE_ACTION_PATH;
    /**
     * /skynet/cluster/topology
     */
    public final String SKYNET_TOPOLOGY_PARAMS_PATH;

    public final String SKYNET_TAGS_PATH;


    private final String clusterName;

    private final String currentActionPlugin;
    private final String currentActionCode;
    private final SkynetZkProperties skynetZKProperties;
    private final ZkConfigService zkConfigService;

    public AntConfigServiceImpl(ZkConfigService zkConfigService, SkynetProperties skynetProperties) throws Exception {

        if (StringUtils.isBlank(skynetProperties.getActionPoint())) {
            throw new Exception("not config [" + SkynetProperties.SKYNET_ACTION_POINT_KEY + "]");
        }

        this.zkConfigService = zkConfigService;
        this.skynetZKProperties = zkConfigService.getZkProperties();

        this.clusterName = skynetZKProperties.getClusterName();

        log.debug("-----------------------------------------");
        log.debug("zookeeper_server:\t{}", skynetZKProperties.getServerList());
        log.debug("zookeeper_cluster:\t{}", clusterName);
        log.debug("-----------------------------------------\n");

        currentActionPlugin = skynetProperties.getPlugin();
        currentActionCode = skynetProperties.getActionCode();

        SKYNET_ROOT_PATH = "/" + clusterName;
        SKYNET_CLUSTER_PATH = SKYNET_ROOT_PATH + "/cluster";
        SKYNET_PLUGIN_PATH = SKYNET_ROOT_PATH + "/plugin";
        SKYNET_SETTINGS_PATH = SKYNET_ROOT_PATH + "/setting";
        SKYNET_ONLINE_PATH = SKYNET_CLUSTER_PATH + "/online";
        SKYNET_ONLINE_ACTION_PATH = SKYNET_ONLINE_PATH + "/action";
        SKYNET_TOPOLOGY_PARAMS_PATH = SKYNET_CLUSTER_PATH + "/topology";
        SKYNET_TAGS_PATH = SKYNET_CLUSTER_PATH + "/tags";

        // 初始化菜单列表缓存，使用LoadingCache自动加载机制，Optional包装避免null值
        // 菜单数据变化不频繁，可以设置较长的缓存时间
        this.menuListCache = CacheBuilder.newBuilder()
                .maximumSize(64)
                .expireAfterAccess(15, TimeUnit.SECONDS)
                .concurrencyLevel(3)
                .recordStats()
                .build(new CacheLoader<>() {
                    @Override
                    @NonNull
                    public Optional<List<AntMenuView>> load(@NonNull String key) throws Exception {
                        log.debug("Loading MenuList from ZK for key: {}", key);
                        return loadMenuListFromZk(key);
                    }
                });
    }

    /**
     * @return the zkConfigService
     */
    @Override
    public ZkConfigService getZkConfigService() {
        return zkConfigService;
    }

    @Override
    public SkynetZkProperties getSkynetZKProperties() {
        return this.skynetZKProperties;
    }

    @Override
    public String getZookeeperServers() {
        return skynetZKProperties.getServerList();
    }

    @Override
    public String getClusterName() {
        return clusterName;
    }

    @Override
    public String getPluginName() {
        return currentActionPlugin;
    }

    @Override
    public String getActionName() {
        return currentActionCode;
    }

    @Override
    public String getTopologyPath() {
        return SKYNET_TOPOLOGY_PARAMS_PATH;
    }

    @Override
    public String getOnlineActionPath() {
        return SKYNET_ONLINE_ACTION_PATH;
    }

    /**
     * 获取版本信息
     *
     * @return
     */
    @Override
    public SkynetVersion getVersion() {
        String path = String.format("%s/version", SKYNET_ROOT_PATH);
        String v = this.getData(path);
        if (StringUtils.isBlank(v)) {
            return null;
        }
        path = String.format("%s/_name", SKYNET_ROOT_PATH);
        String title = this.getData(path);
        SkynetVersion version = new SkynetVersion();
        version.setClusterName(clusterName);
        version.setTitle(title);
        version.setVersion(v);
        return version;
    }

    public boolean exists(String path) {
        return StringUtils.isNoneBlank(path) && this.zkConfigService.exists(path);
    }

    @Override
    public String getData(String path) {
        return this.getData(path, null);
    }

    @Override
    public String getData(String path, Observer dataObserver) {
        return this.exists(path) ? zkConfigService.getData(path, dataObserver) : null;
    }

    @Override
    public void setData(String path, String value) {
        this.zkConfigService.putNode(path, value);
    }

    /**
     * 删除节点数据
     *
     * @param path
     */
    @Override
    public void delData(String path) {
        this.zkConfigService.deleteNode(path);
    }

    public List<String> getChildren(String path) {
        return this.exists(path) ? zkConfigService.getChildren(path) : new ArrayList<>(0);
    }

    /**
     * 得到子节点的数据 【子节点名称(完整路径)和数据】
     *
     * @param path         节点路径，如：/skynet
     * @param dataObserver 子节点个数或每个子节点数据观察器，响应节点的变化
     * @return 子节点名称(完整路径)和数据
     */
    @Override
    public Map<String, String> getChildrenWithData(String path, Observer dataObserver) {
        return this.exists(path) ? zkConfigService.getChildrenWithData(path, dataObserver) : new HashMap<>(0);
    }

    /**
     * 得到子节点的数据 【子节点名称(简短路径)和数据】
     *
     * @param path         节点路径，如：/skynet
     * @param dataObserver 子节点个数或每个子节点数据观察器，响应节点的变化
     * @return 子节点名称(简短路径)和数据
     */
    @Override
    public Map<String, String> getChildrenWithData2(String path, Observer dataObserver) {

        Map<String, String> settings = this.getChildrenWithData(path, dataObserver);
        Map<String, String> resultMap = new TreeMap<>();

        for (Entry<String, String> item : settings.entrySet()) {
            String key = item.getKey().replace(path + "/", "");
            if (!key.startsWith("_")) {
                resultMap.put(key.trim(), item.getValue() != null ? item.getValue().trim() : "");
            }
        }
        return resultMap;
    }

    /**
     * 得到所有子节点的数据（递归获取所有的孩子节点数据）
     *
     * @param path 节点路径，如：/skynet
     * @return 子节点名称(包含简短路径)和数据
     */
    @Override
    public Map<String, String> getAllChildrenWithData2(String path) {
        Map<String, String> settings = this.zkConfigService.exportData(path);
        Map<String, String> resultMap = new TreeMap<>();
        for (Entry<String, String> item : settings.entrySet()) {
            String key = item.getKey().substring(item.getKey().indexOf("=") + 1);
            if (!key.startsWith("_")) {
                resultMap.put(key.trim(), item.getValue() != null ? item.getValue().trim() : null);
            }
        }
        return resultMap;
    }

    @Override
    public void close() throws Exception {
        log.debug("close AntConfigService ...");
        this.zkConfigService.close();
    }

    /**
     * 获取在线状态路径
     *
     * @return
     */
    @Override
    public String getSkynetOnlinePath() {
        return SKYNET_ONLINE_PATH;
    }

    @Override
    public List<String> reportActionNode(AntActionStatus actionStatus) {

        List<String> nodes = new ArrayList<>();
        String path = String.format("%s/%s/%s", SKYNET_ONLINE_PATH, "action", actionStatus.getAction());

        path = String.format("%s/%s", path, actionStatus.getNodeName());
        // 如果存在，先删除，再添加
        if (this.exists(path)) {
            this.delData(path);
        }
        zkConfigService.putEphemeralNode(path, JSON.toJSONString(actionStatus));
        nodes.add(path);
        return nodes;
    }

    /**
     * 取消节点注册
     * 批量删除指定的ZK节点路径
     *
     * @param nodes 要删除的节点路径集合
     */
    @Override
    public void cancelNode(Collection<String> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            log.debug("No nodes to cancel");
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (String path : nodes) {
            if (StringUtils.isBlank(path)) {
                log.warn("Skipping blank node path");
                continue;
            }

            try {
                log.debug("Canceling node path: {}", path);
                this.delData(path);
                successCount++;
            } catch (Exception e) {
                log.warn("Failed to cancel node path: {}", path, e);
                failCount++;
            }
        }

        log.info("Cancel nodes completed: {} success, {} failed", successCount, failCount);
    }

    @Override
    public Map<String, String> getOnlineActionNodes(String actionPoint, OnlineNodeObserver observer) {
        String path = String.format("%s/%s/%s", SKYNET_ONLINE_PATH, "action", actionPoint);
        return this.getChildrenWithData2(path, observer);
    }

    /**
     * 获取skynet信息
     *
     * @return
     */
    @Override
    public NodeDescription getSkynet() {
        String name = this.getData(String.format("%s/_name", SKYNET_ROOT_PATH));
        String desc = this.getData(String.format("%s/_desc", SKYNET_ROOT_PATH));
        return new NodeDescription(clusterName, name, desc);
    }

    @Override
    public String getSkynetPluginPath() {
        return SKYNET_PLUGIN_PATH;
    }

    /**
     * 获取 action 路径 /skynet/plugin/{plugin}/action/{actionName}
     *
     * @param plugin
     * @param action
     * @return
     */
    @Override
    public String getActionPath(String plugin, String action) {
        // /skynet/plugin/{plugin}/action/{actionName}
        return String.format("%s/%s/action/%s", SKYNET_PLUGIN_PATH, plugin, action);
    }

    @Override
    public String getActionPath(String actionId) throws InvalidActionPointException {
        int index = actionId.indexOf('@');
        if (index < 0) {
            throw new InvalidActionPointException("invalid action point : " + actionId);
        }
        String actionCode = actionId.substring(0, index);
        String plugin = actionId.substring(index + 1);
        return getActionPath(plugin, actionCode);
    }

    /**
     * 获取所有插件列表
     * 从ZK中获取所有插件信息并按索引和名称排序
     *
     * @return 插件描述列表，按索引和名称排序
     */
    @Override
    public List<NodeDescription> getPlugins() {
        List<NodeDescription> objList = new CopyOnWriteArrayList<>();
        try {
            List<String> plugins = getChildren(SKYNET_PLUGIN_PATH);
            if (plugins == null || plugins.isEmpty()) {
                log.debug("No plugins found in path: {}", SKYNET_PLUGIN_PATH);
                return objList;
            }

            // 排除下划线开头的系统节点
            plugins.removeIf(plugin -> StringUtils.isBlank(plugin) || plugin.startsWith("_"));
            plugins.parallelStream().forEach(plugin -> {
                try {
                    NodeDescription pluginNode = getPlugin(plugin.trim());
                    if (pluginNode != null) {
                        objList.add(pluginNode);
                    }
                } catch (Exception e) {
                    log.warn("Failed to get plugin info for: {}", plugin, e);
                }
            });
        } catch (Exception e) {
            log.error("Failed to get plugins list", e);
        }
        // 优化排序：先按索引排序，再按名称排序（合并为一次排序操作）
        return objList.stream()
                .sorted(Comparator.comparing(NodeDescription::getIndex)
                        .thenComparing(NodeDescription::getName, Comparator.nullsLast(String::compareTo)))
                .toList();
    }

    @Override
    public NodeDescription getPlugin(String plugin) {

        Assert.hasText(plugin, "the plugin is blank.");
        plugin = plugin.trim();
        NodeDescription nodeDescription = new NodeDescription();
        nodeDescription.setCode(plugin);
        String path = String.format("%s/%s", SKYNET_PLUGIN_PATH, plugin);
        List<String> pluginInfoList = getChildren(path);
        if (pluginInfoList.isEmpty()) {
            log.debug("pluginInfoList is Empty.[plugin={}]", plugin);
            return null;
        }
        for (String pluginInfo : pluginInfoList) {
            String info = this.getData(String.format("%s/%s", path, pluginInfo));
            // 获取plugin基本信息
            switch (pluginInfo) {
                case "_name":
                    nodeDescription.setName(info);
                    break;
                case "_desc":
                    nodeDescription.setDesc(info);
                    break;
                case "_version":
                    nodeDescription.setVersion(info);
                    break;
                case "_index":
                    if (StringUtils.isNumeric(info)) {
                        nodeDescription.setIndex(Integer.parseInt(info));
                    }
                    break;
                default:
            }
        }
        return nodeDescription;

    }

    @Override
    public NodeDescription getPlugin() {
        return getPlugin(currentActionPlugin);
    }

    /**
     * 获取指定插件下的所有Action列表
     *
     * @param plugin 插件名称
     * @return Action描述列表，按索引和名称排序
     * @throws AntException 当插件不存在时抛出异常
     */
    @Override
    public List<NodeDescription> getActions(String plugin) {
        if (StringUtils.isBlank(plugin)) {
            throw new AntException("Plugin name cannot be blank");
        }
        NodeDescription pluginNodeDescription = this.getPlugin(plugin.trim());
        if (pluginNodeDescription == null) {
            throw new AntException(String.format("The plugin [%s] not exist.", plugin));
        }
        try {
            String path = String.format("%s/%s/action", SKYNET_PLUGIN_PATH, plugin.trim());
            List<String> actions = getChildren(path);
            List<NodeDescription> objList = new ArrayList<>();
            if (actions == null || actions.isEmpty()) {
                log.debug("No actions found for plugin: {}", plugin);
                return objList;
            }
            // 排除下划线开头的系统节点
            actions.removeIf(action -> StringUtils.isBlank(action) || action.startsWith("_"));

            // 统一处理action信息收集逻辑
            List<NodeDescription> collected = (actions.size() >= 4)
                    ? actions.parallelStream().map(action -> buildActionNodeDescription(path, action, plugin)).filter(Objects::nonNull).toList()
                    : actions.stream().map(action -> buildActionNodeDescription(path, action, plugin)).filter(Objects::nonNull).toList();
            // 排序
            return collected.stream()
                    .sorted(Comparator.comparing(NodeDescription::getIndex)
                            .thenComparing(NodeDescription::getName, Comparator.nullsLast(String::compareTo)))
                    .toList();
        } catch (Exception e) {
            log.error("Failed to get actions for plugin: {}", plugin, e);
            throw new AntException(String.format("Failed to get actions for plugin [%s]: %s", plugin, e.getMessage()));
        }
    }

    /**
     * 构建单个Action的NodeDescription，异常安全
     */
    private NodeDescription buildActionNodeDescription(String path, String action, String plugin) {
        try {
            String actionTrimmed = action.trim();
            String name = this.getData(String.format("%s/%s/_atitle", path, actionTrimmed));
            String desc = this.getData(String.format("%s/%s/_desc", path, actionTrimmed));
            String index = this.getData(String.format("%s/%s/_index", path, actionTrimmed));
            if (StringUtils.isBlank(name)) {
                return null;
            }
            NodeDescription nodeDescription = new NodeDescription(actionTrimmed, name, desc);
            if (StringUtils.isNumeric(index)) {
                try {
                    nodeDescription.setIndex(Integer.parseInt(index.trim()));
                } catch (NumberFormatException e) {
                    log.warn("Invalid index value for action {}@{}: {}", actionTrimmed, plugin, index);
                }
            }
            return nodeDescription;
        } catch (Exception e) {
            log.warn("Failed to get action info for {}@{}", action, plugin, e);
            return null;
        }
    }

    @Override
    public NodeDescription getAction(String plugin, String action) {
        log.debug("getAction:plugin={};action={}", plugin, action);

        NodeDescription pluginNode = this.getPlugin(plugin);
        if (pluginNode == null) {
            log.debug("getAction:plugin={};action={};pluginNode=null", plugin, action);
            return null;
        }
        String path = this.getActionPath(plugin, action);

        String name = this.getData(String.format("%s/_atitle", path));
        if (StringUtils.isBlank(name)) {
            log.debug("getAction:plugin={};action={};name is blank.", plugin, action);
            return null;
        }

        String desc = this.getData(String.format("%s/%s/_desc", path, action));
        return new NodeDescription(action, name, desc);
    }

    /**
     * <pre>
     * 如果不存将返回Null
     * </pre>
     *
     * @param actionPoint
     * @return
     */
    @Override
    public NodeDescription getAction(String actionPoint) {
        ActionNameContract actionNameContract = new ActionNameContract(actionPoint);
        return getAction(actionNameContract.getPluginCode(), actionNameContract.getActionCode());
    }

    /**
     * 获取 集群级别 Setting 路径
     *
     * @return
     */

    @Override
    public String getSettingPath() {
        return SKYNET_SETTINGS_PATH;
    }

    @Override
    public String getSettingPath(String plugin, String action) {
        if (StringUtils.isBlank(plugin)) {
            return SKYNET_SETTINGS_PATH;
        }
        if (StringUtils.isBlank(action)) {
            return String.format("%s/%s/setting", SKYNET_PLUGIN_PATH, plugin);
        } else {
            return String.format("%s/%s/action/%s/setting", SKYNET_PLUGIN_PATH, plugin, action);
        }
    }

    @Override
    public String getSetting(String key, Observer observer) {
        return this.getSetting(currentActionPlugin, key, observer);
    }

    @Override
    public String getSetting(final String plugin, String key, final Observer observer) {

        // SKYNET_ROOT_PATH/plugin/{plugin}/setting/{key}";
        final String path = getPluginSettingPath(plugin, key);
        String val = this.getData(path, observer);
        if (StringUtils.isBlank(val)) {
            return null;
        }
        return val;
    }

    @Override
    public void setSetting(String plugin, String key, String value) {
        String path = getPluginSettingPath(plugin, key);
        this.setData(path, value);
    }

    /**
     * 获取配置值 如果key 值为空，或没有配置，直接返回 false
     *
     * <pre>
     * 由于是Setting采用继承机制，优先从当前插件的Setting中获取，如果没有将从全局中获取
     * </pre>
     *
     * @param key 配置
     * @return 值
     */

    @Override
    public boolean getBoolSetting(String key, BooleanObserver observer) {
        return getBoolSetting(currentActionPlugin, key, observer);
    }

    /**
     * 获取 某插件下得 配置项 值 如果key 值为空，或没有配置，直接返回 false
     *
     * @param plugin
     * @param key
     * @param observer
     * @return
     */
    @Override
    public boolean getBoolSetting(String plugin, String key, BooleanObserver observer) {
        String strValue = getSetting(plugin, key, observer);
        return "true".equalsIgnoreCase(strValue);
    }

    /**
     * 如果key 值为空，或没有配置，直接返回 Integer.MAX_VALUE
     */
    @Override
    public int getIntegerSetting(String key, IntegerObserver observer) {
        return getIntegerSetting(currentActionPlugin, key, observer);
    }

    /**
     * 如果key 值为空，或没有配置，直接返回 Integer.MAX_VALUE
     */
    @Override
    public int getIntegerSetting(String plugin, String key, IntegerObserver observer) {
        String strValue = getSetting(plugin, key, observer);
        if (StringUtils.isBlank(strValue) || !StringUtils.isNumeric(strValue)) {
            return Integer.MAX_VALUE;
        }
        return Integer.parseInt(strValue);
    }

    /**
     *
     */
    @Override
    public <T> T getTSetting(String key, Class<T> clazz, TObserver<T> observer) {
        return this.getTSetting(currentActionPlugin, key, clazz, observer);
    }

    @Override
    public <T> T getTSetting(String plugin, String key, Class<T> clazz, TObserver<T> tObserver) {

        String strValue = getSetting(plugin, key, tObserver);
        T setting = null;
        if (StringUtils.isNotBlank(strValue)) {
            try {
                setting = JSON.parseObject(strValue, clazz);
            } catch (Exception e) {
                log.error(String.format("getTSetting is error. key:%s; value json: %s", key, strValue), e);
            }
        }
        return setting;
    }

    @Override
    public Map<String, String> getSettings(Observer observer) {
        return getSettings(currentActionPlugin, observer);
    }

    @Override
    public Map<String, String> getSettings(String plugin, Observer observer) {
        String path = StringUtils.isBlank(plugin) ? SKYNET_SETTINGS_PATH : String.format("%s/plugin/%s/setting", SKYNET_ROOT_PATH, plugin);
        Map<String, String> settings = zkConfigService.getChildrenWithData(path, observer);
        Map<String, String> map = new TreeMap<>();
        for (Entry<String, String> item : settings.entrySet()) {
            map.put(item.getKey().replace(SKYNET_SETTINGS_PATH + "/", ""), item.getValue());
        }
        return map;
    }

    private String getPluginSettingPath(String plugin, String key) {
        // skynet/plugin/{plugin}/setting/{key}";
        // 如果plugin 为空 或不存在 ，就 获取全局的配置，否则具体的 plugin下得配置, 这是采用继承的机制
        if (StringUtils.isNotBlank(plugin)) {
            String path = String.format("%s/plugin/%s/setting/%s", SKYNET_ROOT_PATH, plugin, key);
            if (this.exists(path)) {
                return path;
            }
        }

        return String.format("%s/%s", SKYNET_SETTINGS_PATH, key);
    }

    @Override
    public Map<String, String> getGlobalProperties() {
        return getProperties(null, null, null);
    }

    @Override
    public Map<String, String> getGlobalLoggers() throws Exception {
        return getLoggers(null, null, null);
    }

    @Override
    public Map<String, String> getProperties(String plugin, String action, Observer observer) {
        String path = getPropertyPath(plugin, action);
        return getChildrenWithData2(path, observer);
    }

    @Override
    public void setProperty(String plugin, String action, String property, String value) throws Exception {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();
        String path = String.format("%s/%s", getPropertyPath(plugin, action), property);
        this.setData(path, value);
    }

    @Override
    public void delProperty(String plugin, String action, String property) throws Exception {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();
        String path = String.format("%s/%s", getPropertyPath(plugin, action), property);
        this.delData(path);
    }

    @Override
    public Map<String, String> getLoggers(String plugin, String action, Observer observer) {
        String path = getLoggerPath(plugin, action);
        return getChildrenWithData2(path, observer);
    }

    @Override
    public void setLogger(String plugin, String action, String property, String value) throws Exception {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();
        String path = String.format("%s/%s", getLoggerPath(plugin, action), property);
        this.setData(path, value);
    }

    @Override
    public void delLogger(String plugin, String action, String property) throws Exception {
        Assert.hasText(property, "the property is blank.");
        property = property.trim();
        String path = String.format("%s/%s", getLoggerPath(plugin, action), property);
        this.delData(path);
    }

    private String getLoggerPath(String plugin, String action) {
        return getPath(plugin, action, "_logger");
    }

    private String getPropertyPath(String plugin, String action) {
        return getPath(plugin, action, "_properties");
    }

    private String getPath(String plugin, String action, String contextPath) {
        String path;
        if (StringUtils.isNotEmpty(plugin) && StringUtils.isNotEmpty(action)) {// 设置action
            plugin = plugin.trim();
            action = action.trim();
            checkAction(plugin, action);
            path = String.format("%s/%s", this.getActionPath(plugin, action), contextPath);
        } else if (StringUtils.isNotEmpty(plugin)) {// 设置plugin
            plugin = plugin.trim();
            checkPlugin(plugin);
            path = String.format("%s/%s/setting/%s", SKYNET_PLUGIN_PATH, plugin, contextPath);
        } else {
            path = String.format("%s/%s", SKYNET_SETTINGS_PATH, contextPath);
        }
        return path;
    }

    private void checkPlugin(String plugin) {
        NodeDescription pluginNode = this.getPlugin(plugin);
        if (pluginNode == null) {
            throw new AntException("the plugin [%s] not exist.", plugin);
        }
    }

    private void checkAction(String plugin, String action) {
        NodeDescription actionNodeDescription = this.getAction(plugin, action);
        if (actionNodeDescription == null) {
            throw new AntException(String.format("the action [%s@%s] not exist", action, plugin));
        }
    }

    /**
     * ----- Properties & Logger- end -------------------------------------------------------------------------
     */

    @Override
    public Map<String, AntActionStatus> getOnlineActionNodeStatus(String actionPoint, OnlineActionStatusObserver observer) {
        Map<String, AntActionStatus> ret = new TreeMap<>();
        String path = String.format("%s/%s", SKYNET_ONLINE_ACTION_PATH, actionPoint);
        Map<String, String> children = this.zkConfigService.getChildrenWithData(path, observer);
        if (children == null) {
            return ret;
        }
        for (Entry<String, String> entry : children.entrySet()) {
            String key = StringUtils.substringAfterLast(entry.getKey(), "/");
            String value = entry.getValue();
            if (!key.startsWith("_") && StringUtils.isNoneBlank(value) && value.trim().startsWith("{")) {
                try {
                    AntActionStatus status = JSON.parseObject(value, AntActionStatus.class);
                    ret.put(key, status);
                } catch (Throwable t) {
                    //为了兼容 2.1.1009以前的格式转换。
                    JSONObject statusObj = JSON.parseObject(value);
                    if (statusObj.containsKey("start")) {
                        statusObj.replace("start", Calendar.getInstance().get(Calendar.YEAR) + "-" + statusObj.getString("start"));
                        try {
                            AntActionStatus status = JSON.parseObject(statusObj.toJSONString(), AntActionStatus.class);
                            ret.put(key, status);
                        } catch (Throwable t2) {
                            log.error("fail to parse json : key[{}] , value[{}], class:{}", key, value, AntActionStatus.class);
                        }
                    } else {
                        log.error("fail to parse json : key[{}] , value[{}], class:{}", key, value, AntActionStatus.class);
                    }
                }
            }
        }
        return ret;
    }

    @Override
    public List<String> getOnlineActionNames(OnlineActionObserver observer) {
        Map<String, String> settings = this.zkConfigService.getChildrenWithData(SKYNET_ONLINE_ACTION_PATH, observer);

        List<String> result = new ArrayList<>();
        if (settings != null) {
            for (Entry<String, String> entry : settings.entrySet()) {
                String value = entry.getValue();
                String key = StringUtils.substringAfterLast(entry.getKey(), "/");
                if (StringUtils.isBlank(value) && !key.startsWith("_")) {
                    result.add(key);
                }
            }
        }
        return result;
    }

    @Override
    public String filterRefSetting(String srcString, final String path, final Observer observer) {
        return this.filterRefSetting(currentActionPlugin, srcString, path, observer);
    }

    @Override
    public String filterRefSetting(String plugin, String srcString, final String path, final Observer observer) {

        // 替换 {$ref@setting:setting_key} debug by lyhu 2015年02月15日01:08:18
        Map<String, String> refDict = new HashMap<>();
        Matcher matcher = REF_PATTERN.matcher(srcString);
        while (matcher.find()) {
            String key = matcher.group(1);
            if (!refDict.containsKey(key)) {
                refDict.put(key, matcher.group(0));
            }
        }
        for (Entry<String, String> item : refDict.entrySet()) {
            String key = item.getKey();
            String value = this.getSetting(plugin, key, observer == null ? null : (o, arg) -> {
                String s = getData(path);
                observer.update(o, s);
            });
            if (StringUtils.isBlank(value)) {
                value = StringUtils.EMPTY;
                log.info("the setting key:[{}] value is not setting or blank.", key);
            }
            srcString = srcString.replace(item.getValue(), value);
        }
        return srcString;
    }


    //region

    /**
     * 获取服务器参数信息
     * 优先从LoadingCache中获取，如果缓存中不存在则自动从ZK加载
     *
     * @param ip 服务器IP地址
     * @return 服务器参数的克隆对象，如果不存在则返回null
     */
    @Override
    public AntServerParam getServerParam(String ip) {
        return getServerParam(ip, null);
    }

    /**
     * 获取服务器参数信息（带观察器）
     * 直接从ZK获取数据，不使用缓存，适用于需要监听数据变化的场景
     *
     * @param ip                  服务器IP地址
     * @param serverParamObserver 服务器参数变化观察器
     * @return 服务器参数对象，如果不存在则返回null
     */
    @Override
    public AntServerParam getServerParam(String ip, ServerParamObserver serverParamObserver) {
        if (StringUtils.isBlank(ip)) {
            log.warn("IP address is blank when getting server param");
            return null;
        }

        String path = String.format("%s/%s", SKYNET_TOPOLOGY_PARAMS_PATH, ip.trim());
        String jsonData = this.getData(path, serverParamObserver);

        if (StringUtils.isBlank(jsonData)) {
            log.debug("No server param data found for IP: {}", ip);
            return null;
        }

        try {
            AntServerParam antParam = JSON.parseObject(jsonData, AntServerParam.class);
            antParam.setIp(ip.trim());
            return antParam;
        } catch (Exception e) {
            log.error("Failed to parse server param JSON for IP: {}, data: {}", ip, jsonData, e);
            return null;
        }
    }

    /**
     * 删除服务器参数
     * 同时删除ZK中的数据和缓存中的数据
     *
     * @param ip 服务器IP地址
     * @return 删除成功返回true
     */
    @Override
    public boolean delServerParam(String ip) {
        if (StringUtils.isBlank(ip)) {
            log.warn("IP address is blank when deleting server param");
            return false;
        }
        try {
            String path = String.format("%s/%s", SKYNET_TOPOLOGY_PARAMS_PATH, ip.trim());
            this.delData(path);
            log.info("Successfully deleted server param for IP: {}", ip);
            return true;
        } catch (Exception e) {
            log.error("Failed to delete server param for IP: {}", ip, e);
            return false;
        }
    }

    /**
     * 设置服务器参数
     * 将数据保存到ZK并清除缓存以确保数据一致性
     *
     * @param antServerParam 服务器参数对象
     */
    @Override
    public void setServerParam(AntServerParam antServerParam) {
        if (antServerParam == null || StringUtils.isBlank(antServerParam.getIp())) {
            log.warn("Invalid server param: antServerParam is null or IP is blank");
            return;
        }
        try {
            String ip = antServerParam.getIp().trim();
            String path = String.format("%s/%s", SKYNET_TOPOLOGY_PARAMS_PATH, ip);
            // 移除缓存相关代码
            this.setData(path, antServerParam.toString());
            log.debug("Successfully set server param for IP: {}", ip);
        } catch (Exception e) {
            log.error("Failed to set server param for IP: {}", antServerParam.getIp(), e);
        }
    }

    @Override
    public List<AntServerParam> getServers() {
        Map<String, String> servers = this.getChildrenWithData2(SKYNET_TOPOLOGY_PARAMS_PATH, null);
        List<AntServerParam> objList = new ArrayList<>(servers.size());

        for (Entry<String, String> item : servers.entrySet()) {
            String ipAddress = item.getKey();
            AntServerParam antServerParam = this.getServerParam(ipAddress);
            if (antServerParam == null) {
                antServerParam = new AntServerParam();
                antServerParam.setIp(ipAddress);
            }

            objList.add(antServerParam);
        }
        objList = objList.stream().sorted(Comparator.comparing(AntServerParam::getIndex)).toList();
        return objList;
    }

    /**
     * 获取Action参数信息
     * 优先从LoadingCache中获取，如果缓存中不存在则自动从ZK加载
     *
     * @param plugin     插件名称
     * @param actionName Action名称
     * @return Action参数对象
     * @throws ActionNotExistException 当Action不存在时抛出异常
     */
    @Override
    public AntActionParam getActionParam(String plugin, String actionName) throws ActionNotExistException {
        if (StringUtils.isBlank(plugin) || StringUtils.isBlank(actionName)) {
            throw new ActionNotExistException(plugin, actionName);
        }
        try {
            String paramPath = String.format("%s/_param", this.getActionPath(plugin, actionName));
            String paramData = this.getData(paramPath, null);
            AntServiceParam antServiceParam = new AntServiceParam();
            if (StringUtils.isNoneBlank(paramData)) {
                antServiceParam = JSON.parseObject(paramData, AntServiceParam.class);
            }
            return buildActionParam(plugin, actionName, antServiceParam);
        } catch (Exception e) {
            if (e.getCause() instanceof ActionNotExistException) {
                throw (ActionNotExistException) e.getCause();
            }
            log.error("Failed to get ActionParam for plugin: {}, action: {}", plugin, actionName, e);
            throw new ActionNotExistException(plugin, actionName);
        }
    }


    /**
     * 构建Action参数对象
     * 从ZK中获取Action的各种配置信息并组装成完整的参数对象
     *
     * @param plugin          插件名称
     * @param actionName      Action名称
     * @param antServiceParam 服务参数对象
     * @return 完整的Action参数对象
     * @throws ActionNotExistException 当Action不存在时抛出异常
     */
    private AntActionParam buildActionParam(String plugin, String actionName, AntServiceParam antServiceParam) {
        String actionPath = this.getActionPath(plugin, actionName);
        Map<String, String> nodeList = this.getChildrenWithData(actionPath, null);

        if (nodeList.isEmpty()) {
            throw new ActionNotExistException(plugin, actionName);
        }

        // 从ZK节点数据中提取各种配置信息
        String title = nodeList.get(String.format("%s/_atitle", actionPath));
        String desc = nodeList.get(String.format("%s/_desc", actionPath));
        String bootType = nodeList.get(String.format("%s/_xboot_type", actionPath));
        String port = nodeList.get(String.format("%s/_port", actionPath));
        String javaOpts = nodeList.get(String.format("%s/_java_opts", actionPath));
        String xBootParam = nodeList.get(String.format("%s/_xboot_param", actionPath));

        // 构建Action参数对象
        AntActionParam antActionParam = new AntActionParam();
        antActionParam.setPlugin(plugin);
        antActionParam.setService(antServiceParam);
        antActionParam.setCode(actionName);
        antActionParam.setTitle(title);
        antActionParam.setJavaOpts(javaOpts);
        antActionParam.setDesc(desc);

        // 安全解析端口号，默认为0
        try {
            antActionParam.setPort(StringUtils.isBlank(port) ? 0 : Integer.parseInt(port.trim()));
        } catch (NumberFormatException e) {
            log.warn("Invalid port number for action {}@{}: {}, using default 0", actionName, plugin, port);
            antActionParam.setPort(0);
        }

        // 安全解析启动类型，默认为SkynetBoot
        try {
            BootType bootTypeEnum = StringUtils.isBlank(bootType) ?
                    BootType.SkynetBoot : BootType.valueOf(bootType.trim());
            antActionParam.setBootType(bootTypeEnum);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid boot type for action {}@{}: {}, using default SkynetBoot", actionName, plugin, bootType);
            antActionParam.setBootType(BootType.SkynetBoot);
        }

        // 安全解析启动参数
        try {
            BootParam xBoot = StringUtils.isBlank(xBootParam) ?
                    new BootParam() : JSON.parseObject(xBootParam, BootParam.class);
            antActionParam.setBootParam(xBoot);
        } catch (Exception e) {
            log.warn("Invalid boot param JSON for action {}@{}: {}, using default", actionName, plugin, xBootParam, e);
            antActionParam.setBootParam(new BootParam());
        }

        return antActionParam;
    }

    /**
     * tags 样例 ["one","two","three"]
     *
     * @return tagList
     */
    @Override
    public List<String> getTags() {
        String data = this.getData(SKYNET_TAGS_PATH);
        return JSON.parseObject(data, new TypeReference<>() {
        });
    }

    /**
     * @param tags tags ["one","two","three"]
     */
    @Override
    public void updateTags(List<String> tags) {
        // 给 tag 去重
        Set<String> tagsSet = new HashSet<>(tags);
        List<String> tags2 = new ArrayList<>(tagsSet);
        this.setData(SKYNET_TAGS_PATH, JSON.toJSONString(tags2));
    }
    //endregion

    private String getOnlinePath() {
        return getSkynetOnlinePath() + "/menu";
    }

    /**
     * 获取菜单列表
     * 优先从LoadingCache中获取，如果缓存中不存在则自动从ZK加载
     * 使用Optional包装处理null值情况
     *
     * @return 菜单列表，如果不存在则返回空列表
     */
    @Override
    public List<AntMenuView> getMenusList() {
        try {
            // 使用固定的缓存键
            String cacheKey = "MENU_LIST";
            // 使用LoadingCache自动加载机制，返回Optional包装的结果
            Optional<List<AntMenuView>> optionalMenuList = menuListCache.get(cacheKey);
            log.debug("Get MenuList from cache");

            // 返回菜单列表，如果Optional为空则返回空列表
            List<AntMenuView> menuList = optionalMenuList.orElse(new ArrayList<>());
            log.debug("getMenusList= {}", menuList);

            return menuList;
        } catch (Exception e) {
            log.error("Failed to get menu list from cache", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从ZK加载菜单列表数据（CacheLoader使用）
     * 使用Optional包装返回值，避免CacheLoader返回null的问题
     *
     * @param key 缓存键，通常为固定值"MENU_LIST"
     * @return Optional包装的菜单列表，如果不存在则返回Optional.empty()
     */
    private Optional<List<AntMenuView>> loadMenuListFromZk(String key) {
        try {
            List<AntMenuView> menuList = new ArrayList<>();
            String rootPath = getOnlinePath();

            if (!this.exists(rootPath)) {
                log.debug("Menu root path does not exist: {}", rootPath);
                return Optional.of(menuList); // 返回空列表而不是empty
            }

            Map<String, String> menuMap = getChildrenWithData(rootPath, null);
            if (menuMap == null || menuMap.isEmpty()) {
                log.debug("No menu data found in path: {}", rootPath);
                return Optional.of(menuList); // 返回空列表而不是empty
            }

            for (Map.Entry<String, String> item : menuMap.entrySet()) {
                // 排除下划线开头的系统节点
                if (item.getKey().contains("_desc")) {
                    continue;
                }

                try {
                    String menuJson = item.getValue();
                    if (StringUtils.isNoneBlank(menuJson)) {
                        AntMenuView menuView = JSON.parseObject(menuJson, AntMenuView.class);
                        if (menuView != null) {
                            menuList.add(menuView);
                        }
                    }
                } catch (Exception e) {
                    log.warn("Failed to parse menu JSON for key: {}, value: {}", item.getKey(), item.getValue(), e);
                }
            }

            log.debug("Loaded {} menus from ZK", menuList.size());
            return Optional.of(menuList);
        } catch (Exception e) {
            log.error("Failed to load menu list from ZK", e);
            return Optional.of(new ArrayList<>()); // 返回空列表而不是empty
        }
    }

    /**
     * 报告在线菜单
     * 将菜单信息注册到ZK并清除菜单缓存以确保数据一致性
     *
     * @param menuView 菜单视图对象
     * @return 注册的ZK路径
     */
    @Override
    public String reportMenu(AntMenuView menuView) {
        if (menuView == null) {
            log.warn("MenuView is null when reporting menu");
            return null;
        }

        try {
            log.info("Report online menu start, menu：{}", menuView);
            String rootPath = getOnlinePath();

            if (!this.exists(rootPath)) {
                this.setData("%s/%s".formatted(rootPath, "_desc"), "Skynet-UI menu navigation");
            }

            String path = String.format("%s/SkynetUI-%s-%s", rootPath, OsUtil.getIPAddress(), menuView.getMenuMd5());
            this.zkConfigService.putEphemeralNode(path, menuView.toString());

            // 清除菜单缓存，确保下次获取时能获取到最新的菜单列表
            menuListCache.invalidate("MENU_LIST");

            log.info("report online menu end, [{}] register address: {}", menuView.getName(), path);
            return path;
        } catch (Exception e) {
            log.error("Failed to report menu: {}", menuView, e);
            return null;
        }
    }
}
