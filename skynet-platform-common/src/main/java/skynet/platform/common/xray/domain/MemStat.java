package skynet.platform.common.xray.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import oshi.hardware.GlobalMemory;
import skynet.platform.common.xray.annotation.PrometheusMetric;

/**
 * 内存状态
 *
 * <pre>
 * {
 * "usedPercent": 73.83227348327637,
 * "freePercent": 26.167726516723633,
 * "total": 8589934592,
 * "actualUsed": 6342144000,
 * "used": 8540459008,
 * "free": 49475584,
 * "actualFree": 2247790592,
 * "ram": 8192
 * }
 * </pre>
 *
 * <AUTHOR> [2016年8月3日下午1:40:00]
 */
@Getter
@Setter
@Accessors(chain = true)
public class MemStat extends PerfStatBase {

    public static final String METRIC_TYPE = "x-mem";

    @PrometheusMetric("xray_mem_total")
    private long total;

    @PrometheusMetric("xray_mem_used")
    private long used;

    @PrometheusMetric("xray_mem_used_perc")
    private double usedPercent;

    @PrometheusMetric("xray_mem_swap_total")
    private long swapTotal;

    @PrometheusMetric("xray_mem_swap_used")
    private long swapUsed;

    @PrometheusMetric("xray_mem_swap_used_perc")
    private double swapUsedPercent;

    @PrometheusMetric("xray_mem_actual_used")
    private long actualUsed;

    @PrometheusMetric("xray_mem_actual_used_perc")
    private double actualUsedPercent;

    @PrometheusMetric("xray_mem_cached")
    private long cached;

    public MemStat() {
        super(METRIC_TYPE);
    }

//    public MemStat(Mem mem, Swap swap, long cached) {
//        super(METRIC_TYPE);
//        this.total = mem.getTotal();
//        this.used = mem.getUsed();
//        this.usedPercent = (this.total == 0) ? 0.0 : PerfStatBase.format(this.used / (this.total * 1.0f));
//        this.swapTotal = swap.getTotal();
//        this.swapUsed = swap.getUsed();
//        this.swapUsedPercent = (this.swapTotal == 0) ? 0.0 : PerfStatBase.format(this.swapUsed / (this.swapTotal * 1.0f));
//        this.actualUsed = mem.getActualUsed();
//        this.actualUsedPercent = (this.total == 0) ? 0.0 : PerfStatBase.format(this.actualUsed / (this.total * 1.0f));
//        this.cached = cached;
//    }

    public MemStat(GlobalMemory memory, long cached) {
        super(METRIC_TYPE);
        //获取内存总量
        this.total = memory.getTotal();
        //获取已使用内存量
        this.used = (memory.getTotal() - memory.getAvailable());

        //获取已用内存占比
        this.usedPercent = (memory.getTotal() == 0) ? 0.0 : PerfStatBase.format(memory.getAvailable() / (memory.getTotal() * 1.0f));

        //swap总大小
        this.swapTotal = memory.getVirtualMemory().getSwapTotal();
        //使用了多少swap
        this.swapUsed = memory.getVirtualMemory().getSwapUsed();

        //使用的swap的占比
        this.swapUsedPercent = (memory.getVirtualMemory().getSwapTotal() == 0) ? 0.0
                : PerfStatBase.format(memory.getVirtualMemory().getSwapUsed() / (memory.getVirtualMemory().getSwapTotal() * 1.0f));

        //已使用的内存
        this.actualUsed = memory.getTotal() - memory.getAvailable();
        //获取已用内存占比
        this.actualUsedPercent = (memory.getTotal() == 0) ? 0.0
                : PerfStatBase.format((memory.getTotal() - memory.getAvailable()) / (memory.getTotal() * 1.0f));
        this.cached = cached;
    }


    @Override
    public String toString() {
        return super.toString();
    }
}
