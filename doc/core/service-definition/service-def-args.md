<!-- TOC -->

- [运行参数](#运行参数)
  - [1. SpringBoot服务类型](#1-springboot服务类型)
  - [2. SkynetBoot服务类型](#2-skynetboot服务类型)
  - [3. DockerBoot服务类型](#3-dockerboot服务类型)
  - [4. BaseBoot服务类型](#4-baseboot服务类型)

<!-- /TOC -->

# 运行参数

按照服务类型的不同，运行参数的含义也不同

## 1. SpringBoot服务类型

当服务类型是SpringBoot类型时，运行参数的输入代表java -jar xxx.jar 后面的参数输入，即 `public void main(String[] args){}`中的args参数。

> 例如，一个SpringBoot jar的启动命令是:   
> java -Dspring.profiles.active=test -Xmx1g -Xms256m -jar demo.jar --my-arg0=1 --my-arg1=abc   
> 那么，服务定义的`"基本信息=>JVM参数"`部分应该填`"-Dspring.profiles.active=test -Xmx1g -Xms256m"`, `"运行参数"`部分应该填`"--my-arg0=1 --my-arg1=abc"`。

## 2. SkynetBoot服务类型
当服务类型是SpringBoot类型时，运行参数的输入是一个json对象

![alt](res/run-args-skynetboot.png)

|属性|说明|
|-|-|
|name|服务名称，自定义|
|context|服务上下文：key-value形式。在开发过程中，根据服务需要配置相应的属性|

## 3. DockerBoot服务类型

当服务类型是DockerBoot类型时,运行参数( 新版本Tab页标题为'docker参数' ) 的含义是docker create 命令的选项参数输入,例如`'-p -e -v'`等常用选项

一个docker类型服务的运行参数例子
![alt](res/run-args-dockerboot.png)

## 4. BaseBoot服务类型

当服务类型是DockerBoot类型时,运行参数无实际意义，skynet忽略该输入。
