- amd64

```shell
docker build --pull --platform=linux/amd64 -f openjdk{jdk_version}_Dockerfile -t artifacts.iflytek.com/hy-docker-private/skynet/openjdk:{jdk_version}-amd64 .
docker push artifacts.iflytek.com/hy-docker-private/skynet/openjdk:{jdk_version}-amd64
```

- arm64

```shell
docker build --pull --platform=linux/arm64 -f openjdk{jdk_version}_Dockerfile -t artifacts.iflytek.com/hy-docker-private/skynet/openjdk:{jdk_version}-arm64 .
docker push artifacts.iflytek.com/hy-docker-private/skynet/openjdk:{jdk_version}-arm64
```

- manifest

```shell
docker manifest create artifacts.iflytek.com/hy-docker-private/skynet/openjdk:{jdk_version} artifacts.iflytek.com/hy-docker-private/skynet/openjdk:{jdk_version}-amd64 artifacts.iflytek.com/hy-docker-private/skynet/openjdk:{jdk_version}-arm64
docker manifest push artifacts.iflytek.com/hy-docker-private/skynet/openjdk:{jdk_version}
```