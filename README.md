# Skynet 服务托管平台

## 项目概述

Skynet 平台是一个功能全面的微服务管理与部署平台，旨在为企业提供一套完整的、从服务定义、部署、监控到运维的全生命周期管理解决方案。平台设计兼顾了传统虚拟机部署和现代化的 Kubernetes 容器化部署，通过统一的管理控制台，实现了对混合环境的无缝管理。

平台的核心理念是通过一个中心化的管理节点 (`xmanager`) 和部署在各个服务器节点上的代理 (`xagent`)，实现对分布式服务的自动化部署、配置管理、服务发现、健康监控和安全控制。

## 核心特性

-   **混合部署模型**：同时支持在传统物理机/虚拟机和 Kubernetes 集群中部署和管理应用。
-   **统一管理控制台**：提供功能丰富的 Web UI (`xmanager-ui`)，用于可视化地管理所有节点、服务和配置。
-   **集中式配置管理**：使用 ZooKeeper 作为统一的配置中心，支持动态配置下发和更新。
-   **服务发现与负载均衡**：自动注册服务实例到 ZooKeeper，并与 Feign 客户端集成，实现服务间的动态发现和负载均衡。
-   **全生命周期管理**：覆盖了从服务定义、资源上传、部署拓扑规划、一键启停、健康检查到服务下线的完整流程。
-   **深度 Kubernetes 集成**：通过自定义 Operator (`skynet-operator`) 和专为 K8s 设计的 `agent`，将平台能力原生扩展到云原生环境。
-   **企业级功能**：内置了用户认证、操作审计、配置备份与恢复、远程 Web Shell、实时日志查看等企业级功能。



- 服务定义：应用管理、资源管理、配置管理、脚本管理
- 主机管理：节点管理、标签管理、部署包分发、服务分配
- 服务启动：资源部署、升级更新、功能注入、引导启动
- 服务守护：进程守护、健康检测、日志采集、状态画像
- 服务发现：服务上线、服务下线、注册中心（ZooKeeper，TLB）
- 监控预警：主机、组件、业务监控、监控图表、预警管理


## 模块架构

本项目采用模块化的微服务架构，主要包括以下核心模块：

-   **`skynet-platform-xmanager`**: **管理控制台**
    -   平台的“大脑”，提供 Web UI 的后台服务和所有对外 REST API。
    -   负责服务定义、部署拓扑、节点信息等所有元数据的管理，并将其持久化到 ZooKeeper。
    -   内置了对 Kubernetes API 的直接调用能力，可以管理 K8s 原生资源。

-   **`skynet-platform-xagent`**: **节点代理（虚拟机/物理机）**
    -   部署在每个传统服务器节点上的守护进程。
    -   负责接收 `xmanager` 通过 ZooKeeper 下发的指令，管理该节点上所有服务的生命周期（启动、停止、监控）。
    -   支持多种服务类型，包括 SpringBoot、Docker 和普通脚本（BaseBoot）。

-   **`skynet-platform-k8s`**: **Kubernetes 集成模块**
    -   包含 `operator`、`agent` 和 `init` 三个子项目，协同工作以实现在 K8s 中的部署。
    -   `operator`: 监听 `SkynetApp` 自定义资源（CRD），并将其转换为 K8s 的 Deployment、Service 等原生资源。
    -   `agent`: 运行在 K8s 集群内的 `xagent` 替代品，负责将 ZooKeeper 的指令翻译成 `SkynetApp` CRD。
    -   `init`: 作为 Pod 的 `initContainer`，在业务容器启动前，负责从 `xmanager` 拉取配置和依赖文件。

-   **`skynet-platform-common`**: **公共基础模块**
    -   提供了平台所有模块共享的核心代码，包括数据模型、ZooKeeper 客户端、加密工具、系统信息库、缓存和日志工具等。

-   **`skynet-platform-feign`**: **服务间通信客户端**
    -   基于 OpenFeign 的声明式客户端，封装了对 `xmanager` API 的调用，并自动处理服务发现和认证。

-   **`skynet-platform-xmanager-ui`**: **前端 UI 模块**
    -   基于 Vue 和 Element UI 构建的单页应用，为平台提供了丰富的可视化管理界面。

## 技术栈

-   **后端**: Java, Spring Boot
-   **前端**: Node.js, Vue, Element UI
-   **配置与服务发现**: Apache ZooKeeper
-   **容器化**: Docker, Kubernetes
-   **Kubernetes Operator**: Go, Operator SDK (Kubebuilder)
-   **安全**: Spring Security, JWT, Jasypt
-   **通信**: REST, WebSocket, Feign
-   **远程操作**: JSch (SSH)

## 快速开始

1.  **环境准备**: 确保已安装 Java, Maven, Node.js, Docker, 和一个可用的 ZooKeeper 集群。
2.  **编译打包**: 在项目根目录执行 `mvn clean package`。
3.  **部署 `xmanager`**: 将 `skynet-platform-xmanager/target` 下的包部署到一台服务器上，并修改其 `application.properties` 以连接到 ZooKeeper。
4.  **部署 `xagent`**: 将 `skynet-platform-xagent/target` 下的包分发到需要管理的服务器节点上。
5.  **启动服务**: 依次启动 `xmanager` 和各个 `xagent` 服务。
6.  **访问 UI**: 打开浏览器，访问 `http://<xmanager-ip>:<port>` 即可开始使用。

详细的部署和使用指南请参考 `docs` 目录下的相关文档。