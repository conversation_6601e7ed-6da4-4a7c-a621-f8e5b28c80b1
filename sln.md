# 基于 Spring Security 的角色访问控制方案 (修订版)

### 1. 方案概述

本项目利用 Spring Security 的**方法级别安全注解** (`@PreAuthorize`)，为所有对外 REST API 实现基于角色的访问控制。此方案旨在区分 `admin`（管理员）和 `viewer`（观察者）两种角色，并确保向前兼容旧的用户数据结构。

- **`admin`**: 拥有所有接口的完全访问权限，包括创建、读取、更新和删除 (CRUD) 操作。
- **`viewer`**: 仅拥有只读权限，可以访问所有查询（GET）接口，但无权执行任何修改操作。

### 2. 实施计划

#### 步骤 1: 启用方法级别安全

在主启动类 `skynet.platform.manager.Bootstrap` 中，通过添加 `@EnableMethodSecurity` 注解来全局启用方法安全功能。

```java
// skynet.platform.manager.Bootstrap.java
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;

@ServletComponentScan
@EnableAsync
@EnableSkynetWebSocket
@EnableSkynetSecurity
@EnableSkynetSwagger2
@EnableSkynetDiscoveryClient
@EnableSkynetException
// 新增此注解以开启方法级安全控制
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true) 
@ImportResource(locations = {"classpath*:spring/manager/*.xml"})
@SpringBootApplication
public class Bootstrap {
    // ... 类内容保持不变
}
```

---

#### 步骤 2: 更新用户服务 (`SkynetZkUserService`)

修改 `SkynetZkUserService` 以支持两种用户数据结构，并正确加载角色信息。

**用户数据结构:**
- **新结构 (JSON):** 存储在 ZK 路径 `/skynet/users/{username}`，值为 `{"pwd":"...", "roles":"admin,viewer"}`。
- **旧结构 (纯文本):** 存储在 ZK 路径 `/skynet/xmanager/users/{username}`，值为加密后的密码字符串。

**修改逻辑:**
1.  **`findByUsername`**:
    -   优先检查新路径 `/skynet/users/{username}`。如果存在，解析 JSON 获取密码和角色列表。
    -   如果新路径不存在，则检查旧路径 `/skynet/xmanager/users/{username}`。如果存在，使用其值作为密码，并为用户赋予默认的 `VIEWER` 角色。
    -   如果两个路径都不存在，则抛出 `UsernameNotFoundException`。
2.  **`addUser` / `resetPwd`**:
    -   这两个方法将始终在**新路径** `/skynet/users/{username}` 下创建或更新 JSON 格式的用户数据。
3.  **`updatePwd`**:
    -   此方法将同时检查新旧两个路径，以确定用户的位置，并更新相应节点的密码。

---

#### 步骤 3: 为所有 Controller 接口添加权限注解

为所有对外 API 接口添加 `@PreAuthorize` 注解，以强制执行角色权限。

**权限分配原则:**
- **只读操作 (Read-Only)**: 任何 `GET` 请求，允许 `ADMIN` 和 `VIEWER` 角色访问。
  - **注解:** `@PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")`
- **写入操作 (Write)**: 任何 `POST`, `PUT`, `DELETE`, `PATCH` 请求，以及其他执行修改状态的动作（如归档、重启、导入等），仅允许 `ADMIN` 角色访问。
  - **注解:** `@PreAuthorize("hasRole('ADMIN')")`

**示例 (`V3ActionDefinitionController.java`):**
```java
@RestController
public class V3ActionDefinitionController implements V3ActionDefinition {
    // ...
    @Override
    @PreAuthorize("hasAnyRole('ADMIN', 'VIEWER')")
    public GetDefinitionsResponse getDefinitions(...) { /* ... */ }

    @Override
    @PreAuthorize("hasRole('ADMIN')")
    public NoDataResponse createDefinition(@RequestBody ActionDefinitionDto request) { /* ... */ }

    @Override
    @PreAuthorize("hasRole('ADMIN')")
    public NoDataResponse deleteDefinition(...) { /* ... */ }
    // ... 其他接口同样添加注解
}
```

**受影响的 Controller 范围:**
此权限注解将系统地应用于以下包中的所有 Controller：
- `skynet.platform.manager.admin.controller`
- `skynet.platform.manager.k8s.controller`
- `skynet.platform.manager.backup.controller`
- `skynet.platform.manager.controller` (除登录和公共端点外)

---

#### 步骤 4: 异常处理

当认证失败或权限不足时，Spring Security 会自动拦截请求并返回相应的 HTTP 状态码：
- **认证失败**: 返回 `401 Unauthorized`。
- **权限不足**: 返回 `403 Forbidden`。

这确保了系统的安全性和 API 的标准行为，无需额外的异常处理代码。

### 5. 总结

此方案通过标准、声明式的方式为系统增加了强大的安全层。核心修改集中于：
1.  **开启** Spring Security 的方法级安全。
2.  **改造** `SkynetZkUserService` 以支持新旧两种数据源并加载角色。
3.  **全面覆盖** 所有 Controller 接口，应用权限注解。

该方案确保了所有操作都经过严格的权限验证，同时保证了对现有用户数据的兼容性。