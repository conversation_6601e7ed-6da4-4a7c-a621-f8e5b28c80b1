package skynet.platform.agent.grafana.loader;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import skynet.boot.common.domain.RestResponse;
import skynet.platform.agent.grafana.GrafanaProperties;
import skynet.platform.agent.grafana.annotation.ConditionOnGrafana;
import skynet.platform.agent.grafana.domain.RepoFileDo;
import skynet.platform.agent.grafana.exception.TraceableRestClientException;
import skynet.platform.common.repository.OnlineActionManager;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntActionStatus;
import skynet.platform.common.repository.domain.NodeDescription;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <pre>
 *
 * TODO: 注意xManager WebAPI接口 需要授权
 * </pre>
 */
@Slf4j
@Component
@ConditionOnGrafana
public class DashboardRepository {

    private static final String HEADER_MD5 = "MD5";

    private final OnlineActionManager onlineActionManager;
    private final GrafanaProperties grafanaProps;
    private final IAntConfigService antConfigSrv;
    private final RestTemplate signAuthRestTemplate;

    public DashboardRepository(OnlineActionManager onlineActionManager,
                               GrafanaProperties grafanaProps, IAntConfigService antConfigSrv,
                               @Qualifier("authRestTemplate") RestTemplate authRestTemplate) {
        this.onlineActionManager = onlineActionManager;
        this.grafanaProps = grafanaProps;
        this.antConfigSrv = antConfigSrv;
        this.signAuthRestTemplate = authRestTemplate;
    }


    public List<RepoFileDo> list() throws URISyntaxException, TraceableRestClientException, UnsupportedEncodingException {
        List<RepoFileDo> ret = new ArrayList<>();
        String urlPrefix = getUrlPrefix();
        if (StringUtils.isBlank(urlPrefix)) {
            return ret;
        }

        List<String> pluginCodeList = antConfigSrv.getPlugins().stream().map(NodeDescription::getCode).toList();
        for (String pluginCode : pluginCodeList) {
            ret.addAll(this.list(pluginCode, urlPrefix));
        }
        return ret;
    }

    private List<RepoFileDo> list(String plugin, String urlPrefix) throws URISyntaxException, TraceableRestClientException, UnsupportedEncodingException {
        List<RepoFileDo> ret = Collections.emptyList();
        String pluginCodeAfterEncoding = URLEncoder.encode(plugin, StandardCharsets.UTF_8);
        String regexAfterEncoding = URLEncoder.encode(grafanaProps.getDashboardNameRegex(), StandardCharsets.UTF_8);
        String repoListUriString = String.format("%s/repo/list?plugin=%s&regex=%s", urlPrefix, pluginCodeAfterEncoding, regexAfterEncoding);
        log.debug("repoListUriString after url encoding ={}", repoListUriString);
        URI repoListUri = new URI(repoListUriString);
        try {
            ResponseEntity<String> entity = signAuthRestTemplate.getForEntity(repoListUri, String.class);
            String body = entity.getBody();
            log.debug("GET {} return ={}", repoListUriString, body);
            RestResponse<List<RepoFileDo>> restResponse = JSON.parseObject(body, new TypeReference<>() {
            });
            assert restResponse != null;
            if (restResponse.getState().getCode() == 0) {
                List<RepoFileDo> repoFileList = restResponse.getBody();
                if (repoFileList != null) {
                    ret = repoFileList;
                }
            }
        } catch (RestClientException e) {
            throw TraceableRestClientException.build("GET", repoListUriString, e);
        }
        return ret;
    }

    public Dashboard load(RepoFileDo file) throws URISyntaxException, TraceableRestClientException, UnsupportedEncodingException {
        String urlPrefix = getUrlPrefix();
        if (StringUtils.isBlank(urlPrefix)) {
            return null;
        }

        String pluginCodeAfterEncoding = URLEncoder.encode(file.getPlugin(), StandardCharsets.UTF_8);
        String filepathAfterEncoding = URLEncoder.encode(file.getFilePath(), StandardCharsets.UTF_8);
        String downloadUriString = String.format("%s/repo/download?plugin=%s&fileName=%s", urlPrefix, pluginCodeAfterEncoding, filepathAfterEncoding);

        URI downloadUri = new URI(downloadUriString);
        String bodyString;
        String md5 = null;
        try {
            ResponseEntity<byte[]> entity = this.signAuthRestTemplate.getForEntity(downloadUri, byte[].class);
            List<String> md5Headers = entity.getHeaders().get(HEADER_MD5);
            if (md5Headers != null && !md5Headers.isEmpty()) {
                md5 = md5Headers.getFirst();
            }
            byte[] bodyBytes = entity.getBody();
            assert bodyBytes != null;
            bodyString = new String(bodyBytes, StandardCharsets.UTF_8);
        } catch (RestClientException e) {
            throw TraceableRestClientException.build("GET", downloadUriString, e);
        }

        return new Dashboard(bodyString, md5);
    }

    private String getUrlPrefix() {

        List<AntActionStatus> onlineXmanagers = onlineActionManager.getAllNodes(this.grafanaProps.getXmanagerActionPoint());
        if (onlineXmanagers.isEmpty()) {
            log.warn("No online xmanager!");
            return null;
        }
        if (onlineXmanagers.size() > 1) {
            log.warn("Online xmanager is not unique!");
        }
        String urlPrefix = onlineXmanagers.getFirst().getRestWebUri();
        log.debug("xmanager urlPrefix ={}", urlPrefix);
        return urlPrefix;
    }

    // public static void main(String[] args) throws URISyntaxException, UnsupportedEncodingException {
    // RestTemplate rest = new RestTemplate();
    // String uriString =
    // "http://10.1.135.181:2230/skynet/api/v2/repo/_list?plugin=ant-mon&regex=";
    // String regex = "dashboard-.+\\.json";
    // regex = URLEncoder.encode(regex, StandardCharsets.UTF_8);
    // System.out.println("regex after encoding : " + regex);
    // URI uri = new URI(uriString + regex);
    // String body = rest.getForObject(uri, String.class);
    // System.out.println(body);
    // }
}
