package skynet.platform.agent.grafana.loader;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class Datasource {

    private String name;
    private String rawJson;
    private JSONObject json;

    public Datasource() {
    }


    public Datasource(String name, String rawJson, JSONObject json) {
        super();
        this.name = name;
        this.rawJson = rawJson;
        this.json = json;
    }


}
