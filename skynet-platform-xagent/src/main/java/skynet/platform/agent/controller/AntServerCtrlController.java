package skynet.platform.agent.controller;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.plexus.util.cli.Commandline;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.boot.AppContext;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.boot.common.domain.Jsonable;
import skynet.boot.exception.ExceptionExt;
import skynet.platform.agent.core.ScheduledService;
import skynet.platform.agent.core.core.StatusService;
import skynet.platform.agent.server.AntServer;
import skynet.platform.common.domain.BootAction;
import skynet.platform.common.domain.BootStatus;
import skynet.platform.common.repository.domain.AntNodeState;
import skynet.platform.common.utils.cmd.CommandLineExecutor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * xagent 管理 rest 接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@ExposeSwagger2
@RequestMapping(value = "/skynet/agent/ctrl", produces = {MediaType.APPLICATION_JSON_VALUE})
public class AntServerCtrlController {
    private final AntServer antServer;
    private final StatusService statusService;
    private final ScheduledService scheduledService;
    private final AppContext appContext;


    public AntServerCtrlController(AntServer antServer, StatusService statusService, ScheduledService scheduledService, AppContext appContext) {
        this.antServer = antServer;
        this.statusService = statusService;
        this.scheduledService = scheduledService;
        this.appContext = appContext;
    }

    /**
     * 获取 AntServer 状态
     */
    @GetMapping(value = "/FETCH_SERVER_STATE")
    public AntNodeState fetchServerState() {
        return antServer.getNodeState();
    }

    /**
     * 获取 AntWorker 列表状态
     */
    @GetMapping(value = "/FETCH_WORKERS_STATE")
    public List<BootWorkerView> fetchWorkersState() throws IOException {
        List<BootWorkerView> workerViews = new ArrayList<>();
        for (BootStatus bootStatus : statusService.getBoots()) {
            workerViews.add(new BootWorkerView(bootStatus.getProfile().getIndex(), bootStatus.getAid(), bootStatus.getProfile(), bootStatus.getState()));
        }
        return workerViews.stream().sorted(Comparator.comparing(BootWorkerView::getIndex)).toList();
    }

    /***
     * 停止 AntServer 服务
     *
     * @param stopAction  是否包含 托管的Action
     * @return
     */
    @PostMapping(value = "/STOP_SERVER")
    public String stopServer(@RequestParam(value = "stopAction", required = false, defaultValue = "false") boolean stopAction) {

        log.info("The agent will stop.stopAction={}", stopAction);
        new Thread(() -> {
            try {
                if (stopAction) {
                    scheduledService.stopAll();
                }
                scheduledService.close();
            } catch (Exception e) {
                log.error("STOP_SERVER Error.", e);
            }

            // 优先 通过 命令行停止，因为命令行中有进程守护的处理逻辑
            try {
                exec("./ant-xagent.sh " + (stopAction ? "stop" : "kill"));
                Thread.sleep(3000);
            } catch (Exception e) {
                log.error("Stop agent by cmd error={}", e.getMessage());
            }
            System.exit(-1);
        }).start();
        return "the server will stop";
    }

    private void exec(String cmdString) {
        log.info("exec [{}] ...", cmdString);
        Commandline cmd = new Commandline(cmdString);
        cmd.setWorkingDirectory(String.format("%s/bin", appContext.getSkynetHome()));

        try {
            CommandLineExecutor.executeCommandLine(cmd);
        } catch (Exception e) {
            String err = ExceptionExt.mergedMessage(e);
            log.error("exec cmd [{}] error= {}", cmdString, err);
        }
    }


    /**
     * 停止 AntWorker (由于是守护，就是重启)
     */
    @PostMapping(value = "/STOP_WORKER")
    public String stopWorker(@RequestParam(name = "pid") String pid) {
        String response;
        log.info("the ant worker:[pid={}] will stop..", pid);

        try {
            scheduledService.stop(Long.parseLong(pid));
            response = String.format("The worker pid[%s] will stop", pid);
        } catch (Exception e) {
            response = String.format("The worker pid[%s] error: %s", pid, e.getMessage());
        }
        return response;
    }

    /**
     * 停止 AntWorker (由于是守护，就是重启)
     */
    @PostMapping(value = "/REBOOT_WORKER")
    public String rebootWorker(@RequestBody List<String> actionIdList) {
        String response = "OK";
        log.info("Reboot action aids={} ..", actionIdList);
        for (String actionId : actionIdList) {
            try {
                scheduledService.stop(actionId);
            } catch (Exception e) {
                response = String.format("Reboot actionId=%s Error=%s", actionId, e.getMessage());
            }
        }
        return response;
    }


    @Getter
    @Setter
    public static class BootWorkerView extends Jsonable {

        @JSONField(ordinal = 10)
        private final int index;
        @JSONField(ordinal = 20)
        private final String aid;
        @JSONField(ordinal = 30)
        private final BootAction action;
        @JSONField(ordinal = 40)
        private AntNodeState state;

        public BootWorkerView(int index, String aid, BootAction action, AntNodeState state) {
            this.index = index;
            this.aid = aid;
            this.action = action;
            if (state != null) {
                this.state = state;
                this.state.setOs(null);
            }
        }

        @Override
        public String toString() {
            return super.toString();
        }
    }
}
