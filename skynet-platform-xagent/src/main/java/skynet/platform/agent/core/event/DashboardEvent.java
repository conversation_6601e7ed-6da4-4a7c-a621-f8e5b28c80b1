package skynet.platform.agent.core.event;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.filefilter.WildcardFileFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import skynet.boot.common.Files;
import skynet.platform.agent.core.boot.BaseBoot;
import skynet.platform.agent.grafana.loader.DashboardUploader;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Stream;

/**
 * 导入 关联图表 json 配置文件。
 *
 * <pre>
 * 工作目录的doc 目录下  dashboard-.+\\.json
 * </pre>
 *
 * <AUTHOR> [2019年6月16日 下午2:26:42]
 */
@Slf4j
@Service
@RefreshScope
public class DashboardEvent implements BootEvent {

    @Autowired(required = false)
    private DashboardUploader dashboardUploader;

    @Value("${skynet.grafana.dashboardName:dashboard*.json}")
    private String dashboardName;

    /**
     * 相对工作目录的上级 doc目录
     */
    @Value("${skynet.grafana.dashboardDirName:../doc}")
    private String dashboardDirName;

    @Override
    public void onEvent(BaseBoot baseBoot) throws IOException {

        log.debug("[{}] DashboardEvent.onEvent..", baseBoot.getBootParam().getFullName());

        if (dashboardUploader == null) {
            log.warn("skynet.grafana.enabled is false.");
            return;
        }
        if (StringUtils.isBlank(baseBoot.getWorkHome())) {
            log.warn("The boot {} work home is empty.", baseBoot.getBootParam().getFullName());
            return;
        }

        // doc 和工作目录下 所有的 dashboardName
        String docDirName = Files.simplifyPath(Paths.get(baseBoot.getWorkHome(), dashboardDirName).toString());
        List<String> allDocDirList = Stream.of(docDirName, baseBoot.getWorkHome()).distinct().toList();

        FileFilter fileFilter = WildcardFileFilter.builder().setWildcards(dashboardName).setIoCase(IOCase.INSENSITIVE).get();

        for (String docDirName1 : allDocDirList) {
            File docDir = new File(docDirName1);
            log.debug("docDir={};dashboardName={}", docDir, dashboardName);
            if (docDir.exists()) {
                File[] dashboardFiles = docDir.listFiles(fileFilter);
                if (dashboardFiles != null) {
                    for (File file : dashboardFiles) {
                        log.info("Upload the dashboard [{}] to grafana begin..", file);

                        String dashboardJson = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
                        dashboardUploader.create(dashboardJson);
                        log.info("Upload the dashboard [{}] to grafana end.", file);
                    }
                }
            }
        }

    }
}