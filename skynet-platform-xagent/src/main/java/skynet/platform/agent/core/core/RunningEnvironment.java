package skynet.platform.agent.core.core;


import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import skynet.boot.common.ShmDirUtils;
import skynet.boot.zookeeper.SkynetZkProperties;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Getter
@Slf4j
@Service
public class RunningEnvironment {

    private final File actionDirectory;

    public RunningEnvironment(Environment environment, SkynetZkProperties skynetZkProperties) throws IOException {
        ShmDirUtils shmDirUtils = new ShmDirUtils(environment);
        try {
            this.actionDirectory = shmDirUtils.getShmDir(String.format(".%s/action", skynetZkProperties == null ? "skynet" : skynetZkProperties.getClusterName()));
            log.info("init action running directory = {}", actionDirectory);
        } catch (IOException e) {
            log.error("init action running directory error", e);
            throw e;
        }
    }
}
