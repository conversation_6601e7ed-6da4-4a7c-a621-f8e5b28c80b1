<template>
  <div class="agent-detail-main">
    <div class="tab">
      <el-tabs v-model="activeTabName" :lazy="true" class="agent-detail">
        <el-tab-pane :label="$t('537')" name="overview">
          <Overview :agent="agent" />
        </el-tab-pane>
        <el-tab-pane :label="$t('538')" name="diagnosis">
          <Diagnosis :ip="ip" />
        </el-tab-pane>
        <el-tab-pane :label="$t('539')" name="monitor">
          <Monitor :ip="ip" />
        </el-tab-pane>
        <el-tab-pane :label="$t('523')" name="log">
          <LogView :param="logParam" />
        </el-tab-pane>
        <el-tab-pane :label="$t('540')" name="event">
          <LogView :param="eventParam" />
        </el-tab-pane>
        <el-tab-pane :label="$t('541')" name="installation">
          <LogView :param="installationParam" />
        </el-tab-pane>
        <el-tab-pane :label="$t('269')" name="props">
          <props :edit="edit" :configBlockCodes="configBlockCodes" :defaultConfig="defaultConfig" @updateConfigBlockCodes="updateConfigBlockCodes" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import Monitor from './Monitor'
import Overview from './Overview'
import LogView from '../../log/LogView'
import Diagnosis from './Diagnosis'
import { deepCopy } from '@/common/util'
import { encryptUrl } from '@/utils/auth'
import agentApi from '@/axios/api/agent'
export default {
  name: 'AgentDetailMain',
  components: {
    Monitor,
    Overview,
    LogView,
    Diagnosis,
    props: () => import('@/views/ActionDefinition/edit/PropConfig.vue')
  },
  data() {
    return {
      edit: true,
      agent: null,
      ip: '',
      activeTabName: 'overview',
      // logParam: {
      //   title: '',
      //   logUri: '',
      //   deleteUri: '',
      //   downloadUri: '',
      //   isEvent: false
      // },
      // eventParam: {
      //   title: '',
      //   logUri: '',
      //   deleteUri: '',
      //   downloadUri: '',
      //   isEvent: true
      // },
      // installationParam: {
      //   title: '',
      //   logUri: '',
      //   deleteUri: '',
      //   downloadUri: '',
      //   isEvent: false
      // }
      logParam: null,
      eventParam: null,
      installationParam: null,
      basicConfigData: {
        pluginCode: 'ant'
      },
      configBlockCodes: [],
      defaultConfig: {
        prismEditor: {
          show: false
        },
        pluginConfig: {
          show: false
        },
        clusterConfig: {
          show: true,
          canEdit: false
        }
      }
    }
  },
  created() {
    this.ip = this.$route.params.ip
    const thisVue = this
    function getAgent() {
      return new Promise(resolv => {
        if (thisVue.$route.params.agent) {
          resolv(deepCopy(thisVue.$route.params.agent))
        } else if (thisVue.$route.params.ip) {
          thisVue.$api.agent.getAgentByIp(thisVue.$route.params.ip).then(d => {
            resolv(d)
          })
        } else {
          console.error('No agent or ip in route params!')
          resolv({})
        }
      })
    }
    async function init() {
      thisVue.agent = await getAgent()
      let ip = thisVue.agent.ip
      let agentPort = thisVue.agent.serverPort
      let host = window.location.host
      let path = window.location.pathname

      // 设置LogView数据
      let title = `[${ip}${locale.t('542')}`
      let logProxyUri = `ws://${ip}:${agentPort}/skynet/agent/log/ws/ant-xagent@ant`
      let eUrl1 = encryptUrl(logProxyUri)
      let logUri = `ws://${host}${path}skynet/proxy/ws?u=${eUrl1}`
      let eUrl2 = encryptUrl(`http://${ip}:${agentPort}/skynet/agent/log/clean/ant-xagent@ant`)
      let deleteUri = `skynet/proxy/http?u=${eUrl2}`
      let eUrl3 = encryptUrl(`http://${ip}:${agentPort}/skynet/agent/log/download/ant-xagent@ant`)
      let downloadUri = `skynet/proxy/http?u=${eUrl3}`
      thisVue.logParam = {
        title,
        logUri,
        deleteUri,
        downloadUri,
        isEvent: false,
        index: 0
      }
      title = `[${ip}${locale.t('543')}`
      let eventProxyUri = `ws://${ip}:${agentPort}/skynet/agent/event`
      let eUrl4 = encryptUrl(eventProxyUri)
      logUri = `ws://${host}${path}skynet/proxy/ws?u=${eUrl4}`
      thisVue.eventParam = {
        title,
        logUri,
        deleteUri: '',
        downloadUri: '',
        isEvent: true,
        index: 1
      }
      title = `[${ip}${locale.t('544')}`
      logUri = `ws://${host}${path}skynet/api/v3/agents/installation/${ip}/log`
      thisVue.installationParam = {
        title,
        logUri,
        deleteUri: '',
        downloadUri: '',
        isEvent: false,
        index: 2
      }
      return null
    }
    init()
  },
  methods: {
    updateConfigBlockCodes(codes) {
      this.configBlockCodes = codes
      agentApi.updateAgentConfigBlockCodes({
        agentType: 'server',
        ip: this.ip,
        configBlockCodes: codes
      })
    }
  },
  mounted() {
    agentApi.getAgentByIp(this.ip).then(res => {
      let { configBlockCodes } = res
      this.configBlockCodes = configBlockCodes || []
    })
  }
}
</script>
<style scoped lang="scss">
.agent-detail-main {
  background-color: rgba(245, 247, 252, 1);
  height: 100%;
  overflow: auto;
}

.agent-detail-main .tab {
  height: 100%;
  padding: 0 20px;
  background-color: #ffffff;
}
</style>
<style lang="scss">
.agent-detail-main {
  .el-tabs.agent-detail {
    height: 100%;

    & > .el-tabs__content {
      // 55 + 74 + 20*2 + 40 + 15 + 10 = 234
      height: calc(100% - 70px) !important;

      .el-tab-pane {
        height: 100%;
        overflow: auto;
      }
    }
  }
}
</style>
