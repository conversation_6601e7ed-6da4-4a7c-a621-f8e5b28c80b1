<template>
  <el-dialog
    class="ele-mod"
    v-if="dialogVisible"
    :visible.sync="dialogVisible"
    width="820px"
    :close-on-click-modal="false"
    @close="onDialogClose"
    :title="isCreate ? $t('566') : $t('567')"
    :destroy-on-close="true"
  >
    <el-form :model="formData" label-width="140px" label-position="right" :rules="rules" ref="form">
      <el-form-item :label="$t('568')" prop="agentType" style="margin-bottom: 6px;">
        <el-radio-group v-model="formData.agentType" @change="onAgentTypeChange">
          <el-radio :label="'server'">{{ $t('475') }}</el-radio>
          <el-radio :label="'kubernetes'">Kubernetes</el-radio>
          <el-radio :label="'skyos'">skyos</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item :label="$t('569')" prop="ip" v-if="formData.agentType === 'server'">
        <el-input v-model="formData.ip" :disabled="isCreate === false" :placeholder="$t('570')" clearable />
      </el-form-item>
      <el-form-item :label="$t('571')" prop="sshPort" v-if="formData.agentType === 'server'">
        <el-input v-model="formData.sshPort" :placeholder="$t('572')" />
      </el-form-item>
      <el-form-item :label="$t('573')" prop="sshUser" v-if="formData.agentType === 'server'">
        <el-input v-model="formData.sshUser" :placeholder="$t('574')" />
      </el-form-item>
      <el-form-item label="KubeConfig" prop="kubeConfig" v-if="formData.agentType === 'kubernetes'">
        <el-input type="textarea" :rows="10" :placeholder="$t('575')" v-model="formData.kubeConfig"></el-input>
      </el-form-item>
      <el-alert class="custom-alert-description-red" v-if="formData.agentType === 'skyos'" :description="$t('57501')" type="info" style="margin-left: 30px; color: red; margin-bottom: 6px; width: 96.8%;"/>
      <el-form-item label="Cluster Yaml:" prop="skyosCluster" v-if="formData.agentType === 'skyos'">
        <slot name="label">
          <label class="el-form-item__label">
            <el-tooltip
              placement="top"
              effect="dark"
              popper-class="multi-line-tooltip"
            >
              <template #content>
                <div class="tooltip-content">
                  <div class="code-block-wrapper">
                    <el-button
                      type="text"
                      size="mini"
                      class="copy-btn"
                      @click.stop="copyText"
                    >{{ $t('5750') }}</el-button>
                    <pre>{{ $t('5751') }}</pre>
                  </div>
                </div>
              </template>
              <i class="el-icon-info" style="cursor: pointer;"></i>
            </el-tooltip>
          </label>
        </slot>
        <Editor cmMode="yaml" :value.sync="formData.skyosCluster" highlight="true"></Editor>
      </el-form-item>
      <el-form-item :label="$t('576')" prop="registryUrl" v-if="formData.agentType === 'kubernetes' || formData.agentType === 'skyos'">
        <el-input v-model="formData.registryUrl" :placeholder="formData.agentType === 'skyos' ? $t('5761') : $t('577')"/>
      </el-form-item>
      <el-form-item :label="$t('578')" prop="registryUsername" v-if="formData.agentType === 'kubernetes' || formData.agentType === 'skyos'">
        <el-input v-model="formData.registryUsername" :placeholder="formData.agentType === 'skyos' ? $t('5781') : $t('579')"/>
      </el-form-item>
      <el-form-item :label="$t('580')" prop="registryPassword" v-if="formData.agentType === 'kubernetes' || formData.agentType === 'skyos'">
        <el-input v-model="formData.registryPassword" show-password :placeholder="formData.agentType === 'skyos' ? $t('5801'): (isCreate ? $t('581') : $t('582'))"/>
      </el-form-item>
      <el-form-item :label="$t('583')" prop="sshPassword" v-if="formData.agentType === 'server'">
        <el-input v-model="formData.sshPassword" show-password :placeholder="isCreate ? $t('584') : $t('582')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('585')" prop="description">
        <el-input v-model="formData.description" :placeholder="$t('586')"></el-input>
      </el-form-item>
      <el-form-item :label="$t('587')" prop="serverTags">
        <tags-selector ref="server-tags-slector" :selectedTags.sync="formData.serverTags" :getTags="getTags" :updateTags="updateTags" />
      </el-form-item>
      <el-form-item :label="$t('588')" label-width="150px">
        <el-switch v-model="formData.autoInstall" @change="onAutoInstallChange" :disabled="formData.agentType === 'skyos' || !isCreate"> </el-switch>
      </el-form-item>
      <el-form-item :label="$t('589')" label-width="150px" v-if="formData.autoInstall && formData.agentType === 'server'">
        <el-switch v-model="formData.dockerEnabled" :disabled="!formData.autoInstall"> </el-switch>
      </el-form-item>
      <el-form-item :label="$t('5891')" label-width="150px" v-if="formData.autoInstall && formData.agentType === 'skyos'">
        <el-switch v-model="formData.monitoringEnabled" :disabled="!formData.autoInstall"> </el-switch>
      </el-form-item>
      <el-form-item :label="$t('5892')" label-width="150px" v-if="formData.autoInstall && formData.agentType === 'skyos'">
        <el-switch v-model="formData.schedulerEnabled" :disabled="!formData.autoInstall"> </el-switch>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <div class="test-connect">
        <el-button :style="{ float: 'left' }" :loading="isTestConnecting" @click="onTestConnect">
          <span v-if="formData.agentType === 'server'">{{ $t('590') }}</span>
          <span v-else-if="formData.agentType === 'skyos'">{{ $t('5901') }}</span>
          <span v-else>{{ $t('591') }}</span>
        </el-button>
      </div>
      <!-- 点击确认按钮 -->
      <el-button size="mini" icon="el-icon-check" type="primary" @click="onConfirm">{{ $t('75') }}</el-button>
      <el-button size="mini" icon="el-icon-close" @click="onCancel">{{ $t('76') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import TagsSelector from '@/components/TagsSelector/TagsSelector'
import Editor from '@/components/Editor'
import { encryptPassword } from '@/utils/auth'
import agentApi from '@/axios/api/agent'
const emptyFormData = {
  agentType: 'server',
  ip: '',
  sshUser: 'root',
  sshPort: '22',
  sshPassword: '',
  kubeConfig: '',
  skyosCluster: '',
  registryUrl: '',
  registryUsername: '',
  registryPassword: '',
  description: '',
  serverTags: [],
  dockerEnabled: false,
  autoInstall: '',
  monitoringEnabled: false,
  schedulerEnabled: false
}
export default {
  name: 'AgentEditDialog',
  components: {
    TagsSelector,
    Editor
  },
  data() {
    let validateIp = (rule, value, callback) => {
      let reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
      if (!reg.test(value)) {
        callback(new Error(locale.t('592')))
      } else {
        if (this.isCreate) {
          agentApi
            .getAgentByIp(value)
            .then(d => {
              if (d && d.ip === value) {
                callback(new Error(locale.t('593')))
              } else {
                callback()
              }
            })
            .catch(() => {
              callback()
            })
        } else {
          callback()
        }
      }
    }
    let validatePort = (rule, value, callback) => {
      let port = Number(value)
      if (isNaN(port) || port < 0 || port > 65535) {
        return callback(new Error(locale.t('594')))
      }
      return callback()
    }
    let validateUser = (rule, value, callback) => {
      if (value && value.length > 32) {
        return callback(new Error(locale.t('595')))
      }
      return callback()
    }
    let validateDesc = (rule, value, callback) => {
      if (value && value.length > 70) {
        return callback(new Error(locale.t('596')))
      }
      return callback()
    }
    return {
      isTestConnecting: false,
      agentStatus: 0,
      hasSkyosClusterFocused: false,
      rules: {
        ip: [
          {
            required: true,
            message: locale.t('570'),
            trigger: 'blur'
          },
          {
            validator: validateIp,
            trigger: 'blur'
          }
        ],
        sshPort: [
          {
            required: true,
            message: locale.t('572'),
            trigger: 'blur'
          },
          {
            validator: validatePort,
            trigger: 'blur'
          }
        ],
        sshUser: [
          {
            required: true,
            message: locale.t('574'),
            trigger: 'blur'
          },
          {
            validator: validateUser,
            trigger: 'blur'
          }
        ],
        kubeConfig: [
          {
            required: true,
            message: locale.t('597'),
            trigger: 'blur'
          }
        ],
        skyosCluster: [
          {
            required: true,
            message: locale.t('5971'),
            trigger: 'blur'
          }
        ],
        registryUrl: [
          {
            required: true,
            message: locale.t('577'),
            trigger: 'blur'
          }
        ],
        description: [
          {
            validator: validateDesc,
            trigger: 'blur'
          }
        ]
      },
      dialogVisible: false,
      formData: emptyFormData,
      isCreate: true
    }
  },
  created() {},
  methods: {
    open(isCreate, agent) {
      this.dialogVisible = true
      this.isCreate = isCreate
      if (!this.isCreate) {
        this.formData = {
          ip: agent.ip,
          agentType: agent.agentType,
          sshUser: agent.sshUser,
          sshPort: agent.sshPort,
          sshPassword: null,
          kubeConfig: agent.kubeConfig,
          skyosCluster: agent.skyosCluster,
          registryUrl: agent.registryUrl,
          registryUsername: agent.registryUsername,
          registryPassword: null,
          description: agent.description,
          serverTags: agent.serverTags
        }
        delete this.rules.sshPassword
      } else {
        let emptyDup = {}
        Object.assign(emptyDup, emptyFormData)
        this.formData = emptyDup
        this.rules['sshPassword'] = [
          {
            required: true,
            message: locale.t('584'),
            trigger: 'blur'
          }
        ]
      }
    },
    onCancel() {
      this.dialogVisible = false
    },
    onConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let agentParam = this.formatAgentParam()
          if (this.isCreate) {
            this.$api.agent.createAgent(agentParam).then(() => {
              this.$emit('queryList')
              this.onCancel()
            })
          } else {
            this.$api.agent.updateAgent(agentParam).then(() => {
              this.$emit('queryList')
              this.onCancel()
            })
          }
        }
      })
    },
    copyText() {
      const text = this.$t('5751')
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text)
          .then(() => this.$message.success('copy success'))
          .catch(() => this.$message.error('copy failed'))
      } else {
        const textarea = document.createElement('textarea')
        textarea.value = text
        textarea.style.position = 'fixed'
        textarea.style.opacity = '0'
        document.body.appendChild(textarea)
        textarea.select()
        try {
          const result = document.execCommand('copy')
          this.$message.success(result ? 'copy success' : 'copy failed')
        } catch (err) {
          this.$message.error('copy failed')
        }
        document.body.removeChild(textarea)
      }
    },
    onTestConnect() {
      // 校验
      this.$refs.form.validate(valid => {
        if (valid) {
          // 将按钮设置为loading状态
          this.isTestConnecting = true
          let agentParam = this.formatAgentParam()
          this.$api.agent.testConnect(agentParam).finally(() => {
            this.isTestConnecting = false
          })
        }
      })
    },
    formatAgentParam() {
      let agentParam = {}
      Object.assign(agentParam, this.formData)
      if (agentParam.sshPassword) {
        agentParam.sshPassword = encryptPassword(agentParam.sshPassword)
      }
      if (agentParam.registryPassword) {
        agentParam.registryPassword = encryptPassword(agentParam.registryPassword)
      }
      return agentParam
    },
    onDialogClose() {
      // 调用TagsSelector的close方法
      this.$refs['server-tags-slector'].close()
    },
    onAutoInstallChange() {
      if (!this.formData.autoInstall) {
        this.formData.dockerEnabled = false
      }
    },
    getTags() {
      return this.$api.tag.getServerTags()
    },
    updateTags(param) {
      return this.$api.tag.updateServerTags(param)
    },
    onAgentTypeChange(value) {
      this.$refs.form.clearValidate()
      if (value === 'skyos') {
        this.formData.autoInstall = true
      }
    }
  }
}
</script>
<style scoped lang="scss">
.CodeMirror-lint-markers {
  width: 3px;
}

.CodeMirror-linenumbers{
  width: 10px;
}

.custom-alert .el-alert__icon.is-big {
  font-size: 18px; /* 默认大概是 18px，调整为更小 */
  margin-top: 2px;  /* 可根据视觉再微调 */
}

.multi-line-tooltip {
  max-width: 400px;
  white-space: pre-wrap;
  word-break: break-word;
}

.code-block-wrapper {
  position: relative;
  background-color: #2d2d2d;
  border-radius: 6px;
  padding: 8px;
}

.tooltip-content pre {
  margin: 0;
  padding: 8px;
  background-color: rgb(0, 0, 0); /* 深色背景 */
  color: #3dc9b0;            /* 明亮字体 */
  border-radius: 4px;
  font-family: "Courier New", monospace;
  font-size: 12px;
  line-height: 1;
  overflow-x: auto;
}

.copy-btn {
  position: absolute;
  top: -14px;
  right: 6px;
  color: #367ae0;
  font-weight: 500;
  font-size: 12px;
  z-index: 1;
}

.dialog-footer {
  .test-connect {
    span {
      font-size: 13px;
    }

    float: left;

    .el-button {
      margin-left: 20px;
    }
  }
}
</style>
