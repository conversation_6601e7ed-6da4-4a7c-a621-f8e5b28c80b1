<template>
  <div class="action-def-detail" ref="actionDefDetailRef" v-loading="loading">
    <div class="tab">
      <el-tabs v-model="activeTab" type="card" tab-position="top">
        <el-tab-pane :label="$t('264')" name="basic">
          <basic ref="basicConfigRef" v-model="basicConfigData" :edit="edit" :isCreate="isCreate" @type-changed="onTypeChanged" />
        </el-tab-pane>
        <el-tab-pane :label="$t('265')" name="file">
          <files v-model="referencedFilesData" :pluginCode="basicConfigData.pluginCode" :edit="edit" ref="filesRef" />
        </el-tab-pane>
        <el-tab-pane :label="$t('266')" name="file-tpl">
          <file-tpl :data="fileTemplates" :edit="edit" ref="fileTmplRef" />
        </el-tab-pane>
        <el-tab-pane :label="$t('267')" name="function">
          <functions :data="functionData" :defData="originData" :edit="edit" />
        </el-tab-pane>
        <el-tab-pane :label="$t('1214')" name="mesh" v-if="originData && originData.integrationConfig && originData.integrationConfig.meshEnabled">
          <mesh v-model="meshConfigText" :edit="edit"/>
        </el-tab-pane>
        <el-tab-pane :label="$t('268')" name="depend">
          <depend-action :basicConfigData="basicConfigData" :dependActions.sync="dependActions" :edit="edit" />
        </el-tab-pane>
        <el-tab-pane :label="$t('269')" name="props">
          <props
            :pluginCode="basicConfigData.pluginCode"
            v-model="propsData"
            :edit="edit"
            :configBlockCodes="configBlockCodes"
            @updateConfigBlockCodes="
              codes => {
                configBlockCodes = codes
              }
            "
          />
        </el-tab-pane>
        <el-tab-pane :label="$t('270')" name="logging">
          <logging :pluginCode="basicConfigData.pluginCode" v-model="loggingData" :edit="edit" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="vars">
      <vars></vars>
    </div>
    <div class="clear" />
    <div class="operations">
      <div class="op-item" style="margin-top:4px">
        <el-switch
          active-color="#13ce66"
          inactive-color="#E4E7ED"
          :active-text="$t('64')"
          :inactive-text="$t('206')"
          v-model="switchValue"
          @change="onSwitchChange"
        />
      </div>
      <div class="op-item" v-show="edit">
        <el-button size="mini" type="primary" icon="el-icon-folder-checked" @click="onSubmit(true)">{{ $t('271') }}</el-button>
      </div>
      <div class="op-item" v-show="edit">
        <el-button size="mini" type="primary" icon="el-icon-success" @click="onSubmit(false)">{{ $t('88') }}</el-button>
      </div>
      <div class="op-item" v-show="edit">
        <el-button size="mini" type="danger" plain icon="el-icon-refresh-left" @click="reset">{{ $t('272') }}</el-button>
      </div>
      <div class="op-item">
        <el-link type="primary" @click="openToolDialog()" style="margin-top:6px">{{ $t('157') }}</el-link>
      </div>
      <div class="clear" />
    </div>
    <tool ref="toolDialogRef" :modal="true"></tool>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import { objectExtract, deepCopy, copyToClip } from '@/common/util'
import BasicConfig from './BasicConfig'
import ReferencedFiles from './ReferencedFiles'
import FileTemplate from './FileTemplate'
import FunctionConfig from './FunctionConfig'
import PropConfig from './PropConfig'
import MeshConfig from './MeshConfig'
import LoggingConfig from './LoggingConfig'
import DependAction from './DependAction'
import emptyData from '../empty-data'
import Vars from './Vars'
import ToolDialog from '../../Tool/ToolDialog'
import EventBus from '@/components/event/EventBus'

// See src/api/skynet/action-definition.js
const fixedSwitchLabels = [
  {
    code: 'enableHomepage',
    title: locale.t('273'),
    value: false,
    extTitle: locale.t('274'),
    extPlaceHolder: '/',
    extProperty: ''
  },
  {
    code: 'enableLogCollect',
    title: locale.t('275'),
    value: false
  }
]
export default {
  name: 'ActionDefDetail',
  components: {
    basic: BasicConfig,
    files: ReferencedFiles,
    functions: FunctionConfig,
    props: PropConfig,
    mesh: MeshConfig,
    logging: LoggingConfig,
    vars: Vars,
    tool: ToolDialog,
    'file-tpl': FileTemplate,
    'depend-action': DependAction
  },
  mounted() {
    this.varsButtonTop = (document.body.clientHeight - 55 - 75) / 2 + 'px'
  },
  data() {
    return {
      isCreate: false,
      // true: 新建服务定义， false:编辑服务定义
      edit: false,
      originData: null,
      activeTab: 'basic',
      basicConfigData: {},
      referencedFilesData: [],
      fileTemplates: [],
      functionData: [],
      originFunctionData: [],
      propsData: '',
      configBlockCodes: [], // 依赖配置块
      meshConfigText: '',
      loggingData: '',
      dependActions: [],
      varsButtonTop: 0,
      outerPaddingSize: 'normal',
      allSwitchLabels: fixedSwitchLabels,
      __view_type: null,
      loading: false,
      switchValue: false,
      toolDialogVue: ToolDialog
    }
  },
  /* beforeRouteEnter函数在created执行后执行 */
  created() {
    let to = this.$route
    if (to.query.isCreate === true || to.query.isCreate === 'true') {
      let def = to.params.definition ? to.params.definition : emptyData
      this.isCreate = true
      this.edit = true
      this.switchValue = true
      this.originData = def
      this.init(def)
      this.loadAllSwitchLabels()
    } else {
      this.isCreate = false
      this.edit = to.query.edit === 'true' || to.query.edit === true
      this.switchValue = this.edit
      if (to.params.definition) {
        this.originData = to.params.definition
        this.init(to.params.definition)
        this.loadAllSwitchLabels()
      } else if (to.params.id) {
        this.refresh(to.params.id)
      }
    }
  },
  methods: {
    onSwitchChange(val) {
      // console.log('onSwitchChange : %s', val)
      const __this = this
      if (val) {
        this.edit = true
      } else {
        this.$confirm(locale.t('276'), locale.t('66'), {
          confirmButtonText: locale.t('75'),
          cancelButtonText: locale.t('76'),
          type: 'warning'
        })
          .then(() => {
            __this.edit = false
            __this.reset()
          })
          .catch(() => {
            __this.switchValue = true
          })
      }
    },
    refresh(actionPoint, successCallback) {
      const __this = this
      if (!actionPoint) {
        return
      }
      this.loading = true
      this.$api.definition
        .getActionDefinition(actionPoint)
        .then(data => {
          __this.originData = data
          __this.init(data)
          __this.loadAllSwitchLabels()
          if (successCallback) {
            successCallback()
          }
        })
        .finally(() => {
          __this.loading = false
        })
    },
    init(def) {
      let defDuplication = deepCopy(def)
      this.basicConfigData = objectExtract(defDuplication, [
        'pluginCode',
        'pluginName',
        'actionCode',
        'actionName',
        'description',
        'index',
        '__view_type',
        '__view_protocol',
        '__view_sys_envs',
        'port',
        'ports',
        'yaml',
        'startupConfig',
        'healthCheckConfig',
        'tags',
        'replicas',
        'instances'
      ])
      this.__view_type = this.basicConfigData.__view_type
      this.referencedFilesData = defDuplication['__view_ref_files']
      this.propsData = defDuplication['properties']
      this.configBlockCodes = defDuplication['configBlockCodes']
      this.meshConfigText = defDuplication['meshConfigText']
      this.loggingData = defDuplication['loggingLevels']
      this.fileTemplates = defDuplication['extConfigItems']
      this.dependActions = defDuplication['dependActions']
      // this.dependActions = [{ code: 'skynet-lb-dashboard@ant', type: 'runtime' }, { code: 'tlb-v1426@ant', type: 'calling' }, { code: 'tlb-a-v1426@ant', type: 'calling' }, { code: 'tlb-b-v1426@ant', type: 'calling' }]
    },
    reset() {
      this.init(this.originData)
      this.functionData = deepCopy(this.originFunctionData)
      // Add on 2021.03.22
      this.$refs.fileTmplRef.reset()
    },
    onSubmit(exitAfterSave) {
      // Add on 2021.03.22
      this.fileTemplates = this.$refs.fileTmplRef.getFiles()
      let thisVue = this
      this.$refs.basicConfigRef
        .validate()
        .then(() => {
          this.$refs.filesRef
            .validate()
            .then(() => {
              this.$refs.fileTmplRef
                .validate()
                .then(() => {
                  thisVue.doSubmit(exitAfterSave)
                })
                .catch(e => {
                  this.activeTab = 'file-tpl'
                })
            })
            .catch(e => {
              this.activeTab = 'file'
            })
        })
        .catch(e => {
          this.activeTab = 'basic'
        })
    },
    doSubmit(exitAfterSave) {
      this.referencedFilesData = this.referencedFilesData.filter(v => {
        return v.fileName && v.targetDir && v.fileName.trim() && v.targetDir.trim()
      })
      this.propsData = this.propsData ? this.propsData.trim() : ''
      this.loggingData = this.loggingData ? this.loggingData.trim() : ''
      let submitData = {
        ...this.basicConfigData,
        integrationConfig: this.originData.integrationConfig,
        __view_functions: this.functionData,
        __view_ref_files: this.referencedFilesData,
        properties: this.propsData,
        configBlockCodes: this.configBlockCodes,
        dependActions: this.dependActions,
        meshConfigText: this.meshConfigText,
        loggingLevels: this.loggingData,
        extConfigItems: this.fileTemplates
      }

      // console.log('submit : %o', submitData)
      let actionPoint = `${submitData.actionCode}@${submitData.pluginCode}`
      const __this = this
      let cb = () => {
        __this.edit = false
        __this.switchValue = false
        if (exitAfterSave) {
          __this.exit()
        }
      }
      if (this.isCreate) {
        this.$api.definition.createActionDefinition(submitData).then(() => {
          this.refresh(actionPoint, cb)
        })
      } else {
        this.$api.definition.updateActionDefinition(submitData).then(() => {
          this.refresh(actionPoint, cb)
        })
      }
    },
    exit() {
      EventBus.$emit('close-tag', this.$route)
    },
    onVarCopy(text) {
      copyToClip(this, text)
    },
    onTypeChanged(viewType) {
      this.__view_type = viewType
      this.functionData = this.generateFunctionData()
    },
    loadAllSwitchLabels() {
      let thisVue = this
      this.$api.switchLabels.getSwitchLabels().then(d => {
        thisVue.allSwitchLabels = fixedSwitchLabels.concat(d)
        thisVue.originFunctionData = thisVue.generateFunctionData()
        thisVue.functionData = deepCopy(thisVue.originFunctionData)
      })
    },
    generateFunctionData() {
      let bootType = this.__view_type.endsWith('Boot') ? this.__view_type : this.__view_type + 'Boot'
      // 生成针对当前的bootType有效的SwitchLabel
      let activeLabels = new Map()
      for (let item of this.allSwitchLabels) {
        if (!item.boots || item.boots.length === 0 || item.boots.indexOf(bootType) > -1) {
          activeLabels.set(item.code, item)
        }
      }
      // 对该action的已有SwitchLabel值进行过滤，排除已无效的项目
      let filteredData = []
      for (let item of this.originData.__view_functions) {
        let activeLabel = activeLabels.get(item.code)
        if (activeLabel) {
          filteredData.push({
            ...item,
            ...{
              title: activeLabel.title,
              extTitle: activeLabel.extTitle,
              extPlaceHolder: activeLabel.extPlaceHolder
            }
          })
        }
      }
      // 对该action不存在的有效SwitchLabel，生成新的SwitchLabel默认值
      let filteredCodes = new Set(filteredData.map(v => v.code))
      for (let switchLabel of this.allSwitchLabels) {
        if (activeLabels.has(switchLabel.code) && !filteredCodes.has(switchLabel.code)) {
          let itemToAdd = objectExtract(switchLabel, ['code', 'title', 'extPlaceHolder', 'extTitle'])
          itemToAdd['value'] = false
          itemToAdd['extProperty'] = ''
          filteredData.push(itemToAdd)
        }
      }
      return filteredData
    },
    openToolDialog() {
      this.$refs.toolDialogRef.open()
    }
  }
}
</script>
<style lang="scss">
.action-def-detail {
  margin-bottom: 70px;
  height: calc(100% - 50px);

  .tab {
    float: left;
    padding: 10px;
    background-color: #ffffff;
    height: 100%;
    width: calc(80% - 15px);
    margin-right: 15px;
    overflow: auto;

    &:hover {
      box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.15);
    }
  }

  .vars {
    float: left;
    width: 20%;
    height: 100%;
    background-color: #ffffff;

    &:hover {
      box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.15);
    }
  }

  .operations {
    background-color: #ffffff;
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100%;
    box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.15);
    height: 50px;
    padding-top: 11px; // padding-top = (50px - 28px)/2
    padding-right: 20%;

    .op-item {
      float: right;
    }

    .op-item + .op-item {
      margin-right: 20px;
    }
  }

  .el-drawer__body {
    padding: 0 15px 15px 15px;
    overflow-y: auto;
  }

  .not-edit {
    // padding: 0 10px 0 10px;
    word-break: break-all;

    pre {
      word-break: break-all;
      white-space: pre-wrap;
      margin: 0;
    }
  }

  .prompt {
    // padding-left: 10px;
    span {
      padding: 0 10px;
      display: inline-block;
      height: 30px;
      line-height: 30px;
      background-color: rgba(251, 188, 4, 0.1);
    }
  }

  .section-container + .section-container {
    margin-top: 40px;
  }
}
</style>
