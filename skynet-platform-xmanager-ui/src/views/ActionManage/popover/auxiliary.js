import locale from '@/i18n/index.js'
/**
 * 从鼠标事件中寻找包含srcflag属性的元素（参考ActionBadge.vue)
 * 如果没找到，返回mouseEvent.target
 * @param {*} mouseEvent
 */
let findClickSrcElement = function(mouseEvent) {
  let ret = mouseEvent.target
  let checkElement = mouseEvent.target
  while (checkElement) {
    if ('srcflag' in checkElement.attributes) {
      ret = checkElement
      break
    }
    checkElement = checkElement.parentElement
  }
  return ret
}
let findAgentInDeployment = function(deployment, ip) {
  let ret = null
  for (let agentDeploy of deployment) {
    if (agentDeploy.ip === ip) {
      ret = agentDeploy
      break
    }
  }
  return ret
}
let findActionInAgentDeployment = function(agentDeployment, actionID) {
  let ret = null
  for (let action of agentDeployment.actions) {
    if (action.actionID === actionID) {
      ret = action
      break
    }
  }
  return ret
}
export { findClickSrcElement, findAgentInDeployment, findActionInAgentDeployment }
