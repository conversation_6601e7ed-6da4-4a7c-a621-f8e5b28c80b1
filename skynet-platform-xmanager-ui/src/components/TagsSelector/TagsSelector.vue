<template>
  <div class="tags-selector">
    <div class="display">
      <!-- <el-tag v-for="(selectedTag,index) in selectedTags" :key="index">{{selectedTag}}</el-tag> -->
      <el-tag v-for="(tag, index) in selectedTags" :key="index" v-show="index < numFlag">
        {{ tag }}
      </el-tag>
      <el-tag v-if="selectedTags && selectedTags.length >= numFlag + 1"> +{{ selectedTags.length - numFlag }} </el-tag>
    </div>
    <div class="append">
      <el-button size="small" style="border-top-left-radius:0;border-bottom-left-radius:0" @click="onPopperSwitch">{{ $t('124') }}</el-button>
    </div>
    <el-popover trigger="manual" placement="right" width="300" v-model="showPopover" popper-class="tags-selector-popover" @hide="onPopHide">
      <div slot="reference"></div>
      <div class="pop-content">
        <div class="header">
          <div class="left">
            <span>{{ $t('125') }}</span>
          </div>
          <div class="right"><i class="el-icon-close" @click="close" /></div>
          <div class="clear" />
        </div>
        <div class="body">
          <div class="addTag">
            <span v-if="!onInput" @click="addNewTag">
              <el-button type="text" icon="el-icon-plus">{{ $t('126') }}</el-button>
            </span>
            <div v-else class="input">
              <el-input :placeholder="$t('127')" ref="tagInput" v-model="input" size="mini" @keyup.enter.native="onInputConfirm">
                <div slot="append">
                  <i class="el-icon-close cancel" @click="onInputCancel" />
                </div>
              </el-input>
            </div>
          </div>
          <div class="taglist">
            <div v-for="(tagWithCheckbox, index) in tagsWithCheckBox" :key="index" class="tag-check-item">
              <div class="left">
                <el-checkbox v-model="tagWithCheckbox.checked">{{ tagWithCheckbox.tag }}</el-checkbox>
              </div>
              <div class="right">
                <el-popconfirm :title="$t('128')" @onConfirm="onTagRemove(tagWithCheckbox.tag)">
                  <i class="el-icon-close" slot="reference" />
                </el-popconfirm>
              </div>
              <div class="clear" />
            </div>
          </div>
          <div class="footer">
            <el-button size="mini" icon="el-icon-check" type="primary" @click="onConfirm">{{ $t('75') }}</el-button>
            <el-button size="mini" icon="el-icon-close" @click="close">{{ $t('76') }}</el-button>
            <div class="clear" />
          </div>
        </div>
      </div>
    </el-popover>
    <div class="clear" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

export default {
  name: 'TagsSelector',
  created() {
    let thisVue = this
    this.getTags().then(data => {
      thisVue.tags = data || []
    })
  },
  data() {
    return {
      numFlag: 5,
      tags: [],
      tagsWithCheckBox: [],
      showPopover: false,
      onInput: false,
      // 正在输入新标签
      input: ''
    }
  },
  props: {
    selectedTags: {
      type: Array
    },
    getTags: {
      type: Function,
      required: true
    },
    updateTags: {
      type: Function,
      required: true
    }
  },
  computed: {
    tagsSelected: {
      get() {
        return this.selectedTags
      },
      set(val) {
        this.$emit('update:selectedTags', val)
      }
    }
  },
  watch: {
    tags() {
      this.refreshTagsWithCheckbox()
    },
    selectedTags() {
      this.refreshTagsWithCheckbox()
    }
  },
  methods: {
    refreshTagsWithCheckbox() {
      let selectedSet = new Set(this.selectedTags)
      this.tagsWithCheckBox =
        this.tags && this.tags.length > 0
          ? this.tags.map(tag => {
            return {
              checked: selectedSet.has(tag),
              tag
            }
          })
          : []
      // console.log('refreshTagsWithCheckbox : %o , %o', this.tags, this.selectedTags)
      // console.log('refreshTagsWithCheckbox this.tagsWithCheckBox： %o', this.tagsWithCheckBox)
    },
    onPopperSwitch() {
      this.showPopover = !this.showPopover
    },
    close() {
      this.showPopover = false
    },
    addNewTag() {
      this.onInput = true
      this.$nextTick(() => {
        this.$refs.tagInput.focus()
      })
    },
    onInputConfirm() {
      this.onInput = false
      if (this.input) {
        if (this.tags.indexOf(this.input) > 0) {
          this.$message.error(locale.t('129'))
        } else {
          let param = Array.from(this.tags)
          param.push(this.input)
          this.updateTags(param).then(() => {
            this.tags = param
          })
        }
      }
      this.input = ''
    },
    onInputCancel() {
      this.onInput = false
      this.input = ''
    },
    onPopHide() {
      this.onInputCancel()
      // 恢复成初始状态
      this.refreshTagsWithCheckbox()
    },
    onTagRemove(tag) {
      let param = Array.from(this.tags)
      let index = param.indexOf(tag)
      if (index > -1) {
        param.splice(index, 1)
        this.updateTags(param).then(() => {
          this.tags = param
        })
      }
    },
    onConfirm() {
      this.tagsSelected = this.tagsWithCheckBox
        .filter(v => {
          return v.checked
        })
        .map(v => {
          return v.tag
        })
      this.close()
    }
  }
}
</script>
<style scoped lang="scss">
.button-new-tag {
  margin-left: 0px;
  height: 25px;
  line-height: 20px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
.display {
  width: calc(100% - 93px);
  height: 32px;
  line-height: 30px;
  border: 1px solid rgb(220, 223, 230);
  border-right: none;
  padding: 0 15px;
  /* display: inline-block; */
  float: left;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  .el-tag {
    font-size: 12px;
    max-width: 150px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    vertical-align: middle;
  }
  .el-tag + .el-tag {
    margin-left: 10px;
  }
}

.append {
  /* display: inline-block; */
  float: left;
}

.popper {
  width: calc(100% - 56px);
}
.pop-content {
  .header {
    padding: 0 15px;
    height: 30px;
    border-bottom: 1px solid rgb(220, 223, 230);
    background-color: rgba(220, 223, 230, 0.2);
    .left,
    .right {
      font-size: 14px;
      float: left;
      width: 50%;
      height: 30px;
      line-height: 30px;
    }
    .right {
      text-align: right;
      i {
        cursor: pointer;
      }
    }
  } //end .header
  .body {
    padding: 10px 15px;
    .addTag {
      padding: 0 10px 10px 10px;
      border-bottom: 1px solid rgba(220, 223, 230, 0.3);
      .input {
        .confirm {
          color: rgb(103, 194, 58);
          &:hover {
            background-color: rgba(103, 194, 58, 0.3);
            color: #ffffff;
          }
        }
        .cancel {
          color: rgb(245, 108, 108);
          &:hover {
            background-color: rgba(245, 108, 108, 0.3);
            color: #ffffff;
          }
        }
        i {
          display: inline-block;
          width: 26px;
          height: 26px;
          line-height: 26px;
          text-align: center;
          cursor: pointer;
        }
        i + i {
          border-left: 1px solid rgba(220, 223, 230, 0.3);
        }
      } // end .input
    } // end .addTag
    .taglist {
      padding: 10px 0;
      .tag-check-item {
        padding: 4px 10px;
        width: 100%;
        font-size: 12px;
        &:hover,
        &:focus-within {
          background-color: rgb(198, 226, 255);
          .right {
            display: inline;
          }
        }
        .right {
          display: none;
          width: 19px;
          height: 19px;
          line-height: 19px;
          float: left;
          text-align: right;
          cursor: pointer;
          .el-icon-close:before {
            font-size: 16px;
          }
        }
        .left {
          width: calc(100% - 19px);
          float: left;
        }
      }
    } //end .taglist
  } //end .body

  .footer {
    border-top: 1px solid rgba(220, 223, 230, 0.3);
    padding-top: 10px;
    & > button {
      float: right;
      margin-left: 20px;
      font-size: 12px;
    }
  } // end .footer
}
</style>
<style lang="scss">
.el-popover.tags-selector-popover {
  padding: 0;
  background-color: #ffffff;

  span {
    display: inline;
  }
  .addTag .el-input-group__append {
    padding: 0 !important;
    background-color: #ffffff;
  }

  .el-checkbox {
    span {
      display: inline-block;
    }
    width: 100%;
    .el-checkbox__label {
      width: calc(100% - 14px);
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: middle;
    }
  }
}
</style>
