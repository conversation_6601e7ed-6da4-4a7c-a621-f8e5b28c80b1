import locale from '@/i18n/index.js'
import request from '../request'
function loginFtp(data) {
  return request.post('skynet/api/shell/login', data)
}
function getFileTree(sessionId, path) {
  return request.get(`skynet/api/shell/sftp/getFileTree/${sessionId}?path=${path}`)
}
function downloadFile(sessionId, path) {
  let url = `skynet/api/shell/sftp/download/${sessionId}?path=${path}`
  window.open(url, '_blank')
}
function delFiles(sessionId, data) {
  return request.post(`skynet/api/shell/sftp/delete/${sessionId}`, data)
}
function createDir(sessionId, path) {
  return request.post(`skynet/api/shell/sftp/createDir/${sessionId}?path=${path}`)
}
function editFileName(sessionId, oldpath, newpath) {
  return request.put(`skynet/api/shell/sftp/rename/${sessionId}?oldpath=${oldpath}&newpath=${newpath}`)
}
export default {
  loginFtp,
  getFileTree,
  downloadFile,
  delFiles,
  createDir,
  editFileName
}
