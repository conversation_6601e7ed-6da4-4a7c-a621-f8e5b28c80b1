let clusterInfo = {
  'clusterName': 'skynet',
  'zkServers': '111.111.111.111:1234',
  'skynetHome': '/iflytek/server/skynet'
}

let clusterProps = [
  'abc=xxxxxx',
  'sdfsdfsdf=xjiwsejfijeifjsjf',
  'xjljskdjflsjdflsdkfjl=jyyyyyyyyyy',
  'soxjofjsodjfojsdf=lxlcjfsjdlfsldf',
  'jojojjmmm=lowjf.jsljflsf'
].join('\n')

let clusterLoggingLevels = [
  'a.b.c=info',
  'xfs.jj.ji=error',
  'xfs.jj.ji=error',
  'xfs.jj.ji=error',
  'xfs.jj.ji=error',
  'xfs.jj.ji=error'
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error',
  // 'xfs.jj.ji=error'

].join('\n')

export {
  clusterInfo,
  clusterProps,
  clusterLoggingLevels
}
