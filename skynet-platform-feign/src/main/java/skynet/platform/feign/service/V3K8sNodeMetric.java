package skynet.platform.feign.service;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sNodeMetric")
@Tag(name = "v3. K8S NodeMetric 管理", description = "K8S NodeMetric 管理")//, hidden = true)
public interface V3K8sNodeMetric {

    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/nodemetrics", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 NodeMetric 列表")

    SkynetApiResponse<List<JSONObject>> getNodeMetrics(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/nodemetrics/{nodeName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 NodeMetric 详情")

    SkynetApiResponse<JSONObject> getNodeMetric(@PathVariable String ip, @PathVariable String nodeName) throws Exception;

}
