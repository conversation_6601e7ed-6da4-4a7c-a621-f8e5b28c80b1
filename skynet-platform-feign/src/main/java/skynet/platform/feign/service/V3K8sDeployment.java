package skynet.platform.feign.service;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.*;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sDeployment")
@Tag(name = "v3. K8S Deployment 管理", description = "K8S Deployment 管理")//, hidden = true)
public interface V3K8sDeployment {

    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/deployments", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Deployment 列表")

    SkynetApiResponse<List<JSONObject>> getDeployments(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Deployment 详情")

    SkynetApiResponse<JSONObject> getDeployment(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Deployment Yaml")

    SkynetApiResponse<String> getDeploymentYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 Deployment")

    SkynetApiResponse<JSONObject> deleteDeployment(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}/restart", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "重启")

    SkynetApiResponse<JSONObject> restartDeployment(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}/resize", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "伸缩")

    SkynetApiResponse<JSONObject> updateDeploymentReplicas(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName, @RequestBody K8sUpdateReplica k8sUpdateReplica) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}/images", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "调整镜像版本")

    SkynetApiResponse<JSONObject> updateDeploymentImages(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName, @RequestBody K8sUpdateImage k8sUpdateImage) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}/revisionHistoryLimit", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "调整最大历史副本数")

    SkynetApiResponse<JSONObject> updateDeploymentRevisionHistoryLimit(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName, @RequestBody K8sUpdateRevisionHistoryLimit k8sUpdateRevisionHistoryLimit) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}/rollback", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "回滚版本")

    SkynetApiResponse<JSONObject> rollbackDeployment(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName, @RequestBody K8sRollbackDeployment k8sRollbackDeployment) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/deployments/{deploymentName}/strategy", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "修改更新策略")

    SkynetApiResponse<JSONObject> updateDeploymentStrategy(@PathVariable String ip, @PathVariable String namespace, @PathVariable String deploymentName, @RequestBody K8sUpdateDeploymentStrategy k8sUpdateDeploymentStrategy) throws Exception;
}
