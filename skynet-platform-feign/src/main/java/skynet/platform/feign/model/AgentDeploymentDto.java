package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@Setter
@Schema(title = "部署拓扑-服务器节点")
public class AgentDeploymentDto extends Jsonable {

    @Schema(title = "序号")//, position = 10)
    private int index;

    @Schema(title = "服务器IP")//, position = 10)
    private String ip;

    @Schema(title = "agent注册端口")//, position = 20)
    private int agentPort;

    @Schema(title = "agent类型")//, position = 5)
    private String agentType = AgentType.SERVER;

    @Schema(title = "agent服务编码")//, position = 30)
    private String agentActionPoint;

    @Schema(title = "agent服务ID")//, position = 40)
    private String agentActionID;

    @Schema(title = "agent服务状态")//, position = 50)
    private String agentStatus;

    @Schema(title = "副本数")//, position = 52)
    private int replicas = 1;

    @Schema(title = "节点标签")//, position = 55)
    private Map<String, String> nodeSelector;

    @Schema(title = "服务器信息")//, position = 60)
    private Map<String, Object> serverInfo;

    @Schema(title = "服务器业务标签列表")//, position = 70)
    private List<String> serverTags;

    @Schema(title = "服务列表")//, position = 80)
    private List<ActionDeploymentDto> actions = new ArrayList<>(0);

    @Override
    public String toString() {
        return super.toString();
    }

    public String getSort() {
        //如果存在标签按标签第一个属性排序、不存在则按INDEX排序
        return CollectionUtils.isEmpty(this.getServerTags()) ? String.valueOf(this.getIndex()) : this.getServerTags().stream().sorted().collect(Collectors.joining("/"));
    }
}
