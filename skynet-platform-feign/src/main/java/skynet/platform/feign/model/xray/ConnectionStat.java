package skynet.platform.feign.model.xray;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ConnectionStat extends PerfStatBase {

    public static final String METRIC_TYPE = "x-connection";

    /**
     *
     */
    @Schema(title = "xray_tcp_listen")
    private int tcpListen;

    @Schema(title = "xray_tcp_established")
    private int tcpEstablised;

    @Schema(title = "xray_tcp_time_wait")
    private int tcpTimeWait;

    @Schema(title = "xray_tcp_close_wait")
    private int tcpCloseWait;

    @Schema(title = "xray_tcp_last_ack")
    private int tcpLastAck;

    @Schema(title = "xray_tcp_syn_sent")
    private int tcpSynSent;

    /**
     * TCP端口连入总数
     */
    @Schema(title = "xray_tcp_inbound_total")
    private int tcpInboundTotal;

    /**
     * TCP端口连出总数
     */
    @Schema(title = "xray_tcp_outbound_total")
    private int tcpOutboundTotal;

    /**
     * 连入总数
     */
    @Schema(title = "xray_connection_inbound_total")
    private int allInboundTotal;


    /**
     * 连出总数
     */
    @Schema(title = "xray_connection_outbound_total")
    private int allOutboundTotal;

    @Override
    public String toString() {
        return super.toString();
    }
}
